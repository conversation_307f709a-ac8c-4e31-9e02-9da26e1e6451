# 🔧 Debug Dashboard Issues

## 🎯 **Current Issues Identified**

### ❌ **Problem 1: API Data Not Loading**
```
Dashboard Data: 
- overviewData: null
- userActivityData: {activityStats: Array(0), hourlyActivity: Array(0), ...}
- popularContentData: {popularPosts: Array(0), popularTopics: Array(0), ...}
- growthTrendsData: {userGrowth: Array(4), postGrowth: Array(14), ...}
```

### ❌ **Problem 2: Admin Authentication**
```
Login error: { message: 'Invalid credentials' }
```

## 🔍 **Debugging Steps**

### **Step 1: Check Backend Server Status**
```bash
# Check if backend is running
curl http://localhost:5000/api/auth/login

# Should return: {"message":"Email and password are required"}
```

### **Step 2: Check MongoDB Connection**
```bash
# Connect to MongoDB
mongo mongodb://localhost:27017/dien_dan_TVU

# Check collections
show collections

# Check users
db.users.find({email: "<EMAIL>"})

# Check if data exists
db.users.count()
db.posts.count()
db.useractivities.count()
```

### **Step 3: Recreate Admin User**
```bash
cd backend

# Method 1: Direct MongoDB
mongo mongodb://localhost:27017/dien_dan_TVU
db.users.deleteOne({email: "<EMAIL>"})

# Method 2: Node script
node -e "
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');

mongoose.connect('mongodb://localhost:27017/dien_dan_TVU').then(async () => {
    await User.deleteOne({email: '<EMAIL>'});
    
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const admin = new User({
        fullName: 'Administrator',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        status: 'active',
        isEmailVerified: true
    });
    
    await admin.save();
    console.log('Admin created successfully');
    process.exit(0);
}).catch(console.error);
"
```

### **Step 4: Test API Endpoints**
```bash
# Test login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Should return: {"success":true,"token":"...","user":{...}}

# Test analytics with token
curl -X GET "http://localhost:5000/api/admin/analytics/overview" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🛠️ **Quick Fix Solutions**

### **Solution 1: Manual Admin Creation**
```javascript
// Run in MongoDB shell
use dien_dan_TVU

db.users.insertOne({
    fullName: "Administrator",
    email: "<EMAIL>",
    password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // admin123
    role: "admin",
    status: "active",
    isEmailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date()
})
```

### **Solution 2: Frontend Token Check**
```javascript
// Add to AdminDashboardOverview.jsx
const getAuthToken = () => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    console.log('User from localStorage:', user);
    console.log('Token:', user.token);
    return user.token;
};
```

### **Solution 3: API Error Handling**
```javascript
// Enhanced error logging in fetchOverviewData
try {
    const token = getAuthToken();
    if (!token) {
        console.error('No auth token found');
        setError('Không tìm thấy token xác thực. Vui lòng đăng nhập lại.');
        return;
    }
    
    console.log('Making API call to:', `${API_BASE_URL}/admin/analytics/overview`);
    const response = await axios.get(`${API_BASE_URL}/admin/analytics/overview`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { days: 30 }
    });
    
    console.log('Full API response:', response);
    
} catch (error) {
    console.error('API Error Details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
    });
}
```

## 🔄 **Step-by-Step Recovery Process**

### **1. Restart Backend Server**
```bash
cd backend
# Kill existing process
pkill -f "node index.js"
# Start fresh
node index.js
```

### **2. Verify Database Data**
```bash
# Check if sample data exists
mongo mongodb://localhost:27017/dien_dan_TVU
db.useractivities.count()  // Should be 1000+
db.posts.count()           // Should be 15+
db.users.count()           // Should be 15+
```

### **3. Test Login Flow**
```bash
# 1. Create admin user
node scripts/createAdminUser.js

# 2. Test login API
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# 3. Copy token and test analytics
curl -X GET "http://localhost:5000/api/admin/analytics/overview" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **4. Frontend Debug**
```javascript
// Add to browser console
localStorage.getItem('user')  // Check stored user data
```

## 🎯 **Expected Working Flow**

### **1. Successful Login Response**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "_id": "...",
    "fullName": "Administrator",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### **2. Successful Overview API Response**
```json
{
  "success": true,
  "data": {
    "totals": {
      "users": 15,
      "posts": 15,
      "topics": 8,
      "comments": 100,
      "likes": 200
    },
    "period": {
      "days": 30,
      "newUsers": 15,
      "newPosts": 15,
      "newComments": 100,
      "activeUsers": 15
    },
    "growth": {
      "userGrowthRate": 0,
      "postGrowthRate": 0
    }
  }
}
```

### **3. Working Dashboard Display**
```
✅ Overview Cards: Show real numbers (15 users, 15 posts, etc.)
✅ Growth Trends: Line chart with real data points
✅ User Activity: Doughnut chart with activity types
✅ Category Stats: Bar chart with topic categories
✅ Popular Content: Lists with real posts and topics
```

## 🚨 **Emergency Fallback**

If all else fails, use the fallback data that's already implemented:

```javascript
// The dashboard will show meaningful sample data
// All charts will display with realistic Vietnamese university context
// Users can see the UI/UX even without real API data
```

---

**🔧 Debug process complete!**

**Next Steps:**
1. Check MongoDB connection
2. Recreate admin user
3. Test API endpoints
4. Verify frontend token handling
5. Check browser console for detailed errors

**🌐 Once fixed, dashboard should show real data at:**
http://localhost:5174/admin
