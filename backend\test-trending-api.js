// Test Trending Topics API
require('dotenv').config();
const mongoose = require('mongoose');
const Post = require('./models/Post');
const Topic = require('./models/Topic');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
};

const testTrendingTopics = async () => {
    await connectDB();
    
    console.log('\n🔥 Testing Trending Topics API...\n');
    
    try {
        // Test the same aggregation as in the API
        const trendingTopics = await Topic.aggregate([
            {
                $lookup: {
                    from: 'posts',
                    localField: '_id',
                    foreignField: 'topicId',
                    as: 'posts'
                }
            },
            {
                $addFields: {
                    postCount: { $size: '$posts' },
                    recentPostCount: {
                        $size: {
                            $filter: {
                                input: '$posts',
                                cond: {
                                    $gte: ['$$this.createdAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)]
                                }
                            }
                        }
                    },
                    trendingScore: {
                        $add: [
                            { $multiply: [{ $size: '$posts' }, 2] },
                            { $multiply: [{ $size: { $filter: { input: '$posts', cond: { $gte: ['$$this.createdAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] } } } }, 5] },
                            { $cond: ['$trending', 100, 0] }
                        ]
                    }
                }
            },
            {
                $sort: {
                    trending: -1,
                    trendingScore: -1,
                    postCount: -1
                }
            },
            {
                $project: {
                    name: 1,
                    description: 1,
                    color: 1,
                    category: 1,
                    icon: 1,
                    trending: 1,
                    postCount: 1,
                    recentPostCount: 1,
                    trendingScore: 1,
                    createdAt: 1
                }
            },
            { $limit: 8 }
        ]);

        console.log('📊 Trending Topics Results:');
        console.log('='.repeat(50));
        
        trendingTopics.forEach((topic, index) => {
            console.log(`${index + 1}. ${topic.name}`);
            console.log(`   📚 Category: ${topic.category}`);
            console.log(`   🔥 Trending: ${topic.trending ? 'YES' : 'NO'}`);
            console.log(`   📝 Post Count: ${topic.postCount}`);
            console.log(`   📅 Recent Posts (7 days): ${topic.recentPostCount}`);
            console.log(`   🎯 Trending Score: ${topic.trendingScore}`);
            console.log(`   🎨 Color: ${topic.color || 'Not set'}`);
            console.log('   ' + '-'.repeat(40));
        });

        console.log(`\n📈 Total trending topics found: ${trendingTopics.length}`);

        // Also test individual topic post counts
        console.log('\n🔍 Individual Topic Analysis:');
        console.log('='.repeat(50));
        
        const allTopics = await Topic.find();
        for (const topic of allTopics) {
            const postCount = await Post.countDocuments({ topicId: topic._id });
            console.log(`📚 ${topic.name}: ${postCount} posts (trending: ${topic.trending ? 'YES' : 'NO'})`);
        }

    } catch (error) {
        console.error('❌ Error testing trending topics:', error);
    }
    
    mongoose.connection.close();
};

testTrendingTopics();
