const mongoose = require('mongoose');
const Topic = require('./models/Topic');

async function checkAndUpdateTrending() {
    try {
        await mongoose.connect('mongodb://localhost:27017/dien_dan_TVU');
        console.log('Connected to MongoDB');

        // Tìm tất cả chủ đề
        const allTopics = await Topic.find({});
        console.log('All topics:');
        allTopics.forEach(topic => {
            console.log(`- ${topic.name}: trending=${topic.trending}`);
        });

        // Tìm chủ đề "Trí tuệ nhân tạo"
        const aiTopic = await Topic.findOne({ 
            name: { $regex: /trí tuệ nhân tạo/i } 
        });

        if (aiTopic) {
            console.log(`\nFound AI topic: ${aiTopic.name}, current trending: ${aiTopic.trending}`);
            
            // Cập nhật trending = true
            aiTopic.trending = true;
            await aiTopic.save();
            console.log('Updated AI topic trending to true');
        } else {
            console.log('\nAI topic not found, creating one...');
            
            // Tạo chủ đề mới
            const newTopic = new Topic({
                name: 'Trí tuệ nhân tạo',
                description: 'Thảo luận về AI, Machine Learning và các công nghệ thông minh',
                category: 'academic',
                color: '#9C27B0',
                trending: true,
                status: 'active',
                createdBy: new mongoose.Types.ObjectId() // Tạm thời dùng ObjectId random
            });
            
            await newTopic.save();
            console.log('Created new AI topic with trending=true');
        }

        // Kiểm tra lại các chủ đề trending
        const trendingTopics = await Topic.find({ trending: true });
        console.log('\nTrending topics after update:');
        trendingTopics.forEach(topic => {
            console.log(`- ${topic.name}: trending=${topic.trending}`);
        });

        mongoose.disconnect();
        console.log('\nDisconnected from MongoDB');
    } catch (error) {
        console.error('Error:', error);
        mongoose.disconnect();
    }
}

checkAndUpdateTrending();
