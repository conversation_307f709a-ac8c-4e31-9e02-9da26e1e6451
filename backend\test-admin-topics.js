// File: backend/test-admin-topics.js
// Script để test các API quản lý chủ đề cho admin

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Token admin (cần thay thế bằng token thực tế)
const ADMIN_TOKEN = 'your-admin-token-here';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
    }
});

// Test functions
async function testGetAllTopics() {
    console.log('\n=== Test: Get All Topics ===');
    try {
        const response = await api.get('/admin/topics', {
            params: {
                page: 1,
                limit: 5,
                search: '',
                category: '',
                status: ''
            }
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testGetTopicStats() {
    console.log('\n=== Test: Get Topic Stats ===');
    try {
        const response = await api.get('/admin/topics/stats');
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testCreateTopic() {
    console.log('\n=== Test: Create Topic ===');
    try {
        const response = await api.post('/admin/topics', {
            name: 'Test Topic - ' + Date.now(),
            description: 'Đây là chủ đề test được tạo bởi script',
            category: 'Học tập',
            priority: 5,
            color: '#ff5722',
            icon: 'school',
            tags: ['test', 'demo'],
            isVisible: true,
            allowPosts: true,
            requireApproval: false
        });
        console.log('✅ Success:', response.data);
        return response.data.data._id; // Return topic ID for further tests
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
        return null;
    }
}

async function testGetTopicById(topicId) {
    console.log('\n=== Test: Get Topic By ID ===');
    try {
        const response = await api.get(`/admin/topics/${topicId}`);
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testUpdateTopic(topicId) {
    console.log('\n=== Test: Update Topic ===');
    try {
        const response = await api.put(`/admin/topics/${topicId}`, {
            description: 'Mô tả đã được cập nhật - ' + new Date().toLocaleString(),
            priority: 8,
            color: '#4caf50'
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testUpdateTopicStatus(topicId) {
    console.log('\n=== Test: Update Topic Status ===');
    try {
        const response = await api.put(`/admin/topics/${topicId}/status`, {
            status: 'inactive'
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testBulkUpdatePriority(topicIds) {
    console.log('\n=== Test: Bulk Update Priority ===');
    try {
        const topics = topicIds.map((id, index) => ({
            id,
            priority: index + 1
        }));

        const response = await api.put('/admin/topics/bulk-update-priority', {
            topics
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testDeleteTopic(topicId) {
    console.log('\n=== Test: Delete Topic ===');
    try {
        const response = await api.delete(`/admin/topics/${topicId}`);
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testUpdatePostCounts() {
    console.log('\n=== Test: Update Post Counts ===');
    try {
        const response = await api.put('/admin/topics/update-post-counts');
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testFixTopicData() {
    console.log('\n=== Test: Fix Topic Data ===');
    try {
        const response = await api.put('/admin/topics/fix-data');
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting Admin Topic Management API Tests...');
    console.log('⚠️  Make sure to replace ADMIN_TOKEN with a valid admin token');

    // Test basic endpoints
    await testGetTopicStats();
    await testGetAllTopics();
    await testUpdatePostCounts();
    await testFixTopicData();

    // Test CRUD operations
    const newTopicId = await testCreateTopic();

    if (newTopicId) {
        await testGetTopicById(newTopicId);
        await testUpdateTopic(newTopicId);
        await testUpdateTopicStatus(newTopicId);

        // Test bulk update with the new topic
        await testBulkUpdatePriority([newTopicId]);

        // Clean up - delete the test topic
        await testDeleteTopic(newTopicId);
    } else {
        console.log('\n⚠️  Skipping topic-specific tests due to creation failure.');
    }

    console.log('\n✨ Tests completed!');
}

// Test data for creating multiple topics
const sampleTopics = [
    {
        name: 'Lập trình Web',
        description: 'Thảo luận về các công nghệ web hiện đại như React, Vue, Angular',
        category: 'Công nghệ',
        priority: 9,
        color: '#2196f3',
        icon: 'code',
        tags: ['web', 'javascript', 'frontend', 'backend']
    },
    {
        name: 'Thực tập doanh nghiệp',
        description: 'Chia sẻ kinh nghiệm thực tập và cơ hội việc làm',
        category: 'Thực tập',
        priority: 8,
        color: '#ff9800',
        icon: 'work',
        tags: ['thực tập', 'việc làm', 'kinh nghiệm']
    },
    {
        name: 'Nghiên cứu khoa học',
        description: 'Các đề tài nghiên cứu và phương pháp khoa học',
        category: 'Nghiên cứu',
        priority: 7,
        color: '#9c27b0',
        icon: 'science',
        tags: ['nghiên cứu', 'khoa học', 'luận văn']
    }
];

// Function to create sample topics
async function createSampleTopics() {
    console.log('\n🌱 Creating sample topics...');

    for (const topicData of sampleTopics) {
        try {
            const response = await api.post('/admin/topics', topicData);
            console.log(`✅ Created topic: ${topicData.name}`);
        } catch (error) {
            console.log(`❌ Failed to create topic ${topicData.name}:`, error.response?.data?.message || error.message);
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const args = process.argv.slice(2);

    if (args.includes('--create-samples')) {
        createSampleTopics().catch(console.error);
    } else {
        runTests().catch(console.error);
    }
}

module.exports = {
    testGetAllTopics,
    testGetTopicStats,
    testCreateTopic,
    testGetTopicById,
    testUpdateTopic,
    testUpdateTopicStatus,
    testBulkUpdatePriority,
    testDeleteTopic,
    testUpdatePostCounts,
    testFixTopicData,
    createSampleTopics
};
