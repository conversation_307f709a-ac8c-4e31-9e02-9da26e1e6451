require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

console.log('🚀 Starting chat test server...');

const app = express();
const server = http.createServer(app);

// CORS configuration
const corsOptions = {
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));
app.use(express.json());

// Socket.IO configuration
const io = socketIo(server, {
    cors: corsOptions
});

console.log('✅ Express and Socket.IO configured');

// In-memory storage for testing
const onlineUsers = new Map(); // userId -> {socketId, lastSeen}
const messages = new Map(); // messageId -> message object

// Test routes
app.get('/test', (req, res) => {
    res.json({ 
        message: 'Chat test server is working!', 
        timestamp: new Date(),
        onlineUsers: Array.from(onlineUsers.keys()),
        totalMessages: messages.size
    });
});

app.get('/api/test', (req, res) => {
    res.json({ 
        message: 'API endpoint working!',
        onlineUsers: onlineUsers.size
    });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('🔌 New socket connection:', socket.id);

    // Handle user joining
    socket.on('joinUserRoom', (userId) => {
        console.log('🏠 User joining room:', userId, 'Socket:', socket.id);
        
        // Store user connection
        onlineUsers.set(userId.toString(), {
            socketId: socket.id,
            lastSeen: new Date().toISOString()
        });
        
        socket.userId = userId;
        
        console.log('👤 User connected:', userId);
        console.log('📊 Total online users:', onlineUsers.size);
        
        // Send test confirmation
        socket.emit('test', {
            message: 'Connection successful',
            userId: userId,
            socketId: socket.id,
            timestamp: new Date().toISOString()
        });
        
        // Broadcast user online status
        io.emit('userOnline', {
            userId: userId,
            isOnline: true
        });
    });

    // Handle sending messages
    socket.on('sendMessage', (data) => {
        console.log('📨 Message received:', data);
        
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const message = {
            _id: messageId,
            content: data.content,
            senderId: data.senderId,
            receiverId: data.receiverId,
            messageType: data.messageType || 'text',
            attachments: data.attachments || [],
            status: 'sent',
            createdAt: new Date(),
            isRead: false
        };
        
        // Store message
        messages.set(messageId, message);
        
        console.log('💾 Message stored:', messageId);
        
        // Send confirmation to sender
        socket.emit('messageSent', {
            success: true,
            message: message,
            tempId: data.tempId
        });
        
        // Send to receiver if online
        const receiverData = onlineUsers.get(data.receiverId.toString());
        if (receiverData) {
            console.log('📤 Sending to receiver:', data.receiverId, 'Socket:', receiverData.socketId);
            io.to(receiverData.socketId).emit('newMessage', message);
            
            // Update message status
            message.status = 'delivered';
        } else {
            console.log('❌ Receiver not online:', data.receiverId);
        }
        
        // Also send to sender for sync
        const senderData = onlineUsers.get(data.senderId.toString());
        if (senderData && senderData.socketId !== socket.id) {
            io.to(senderData.socketId).emit('newMessage', message);
        }
    });

    // Handle message deletion
    socket.on('deleteMessage', (data) => {
        console.log('🗑️ Delete message request:', data);
        
        const message = messages.get(data.messageId);
        if (message && message.senderId === data.userId) {
            // Mark as deleted
            message.isDeleted = true;
            message.deletedAt = new Date();
            
            console.log('✅ Message marked as deleted:', data.messageId);
            
            // Notify both sender and receiver
            const senderData = onlineUsers.get(message.senderId.toString());
            const receiverData = onlineUsers.get(message.receiverId.toString());
            
            const deleteNotification = { messageId: data.messageId };
            
            if (senderData) {
                io.to(senderData.socketId).emit('messageDeleted', deleteNotification);
            }
            
            if (receiverData) {
                io.to(receiverData.socketId).emit('messageDeleted', deleteNotification);
            }
        } else {
            console.log('❌ Cannot delete message - not found or no permission');
        }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
        console.log('🔌 Socket disconnected:', socket.id);
        
        if (socket.userId) {
            onlineUsers.delete(socket.userId.toString());
            console.log('👤 User disconnected:', socket.userId);
            console.log('📊 Remaining online users:', onlineUsers.size);
            
            // Broadcast user offline status
            io.emit('userOffline', {
                userId: socket.userId,
                isOnline: false
            });
        }
    });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
    console.log(`🚀 Chat test server running on port ${PORT}`);
    console.log(`📡 Socket.IO server ready`);
    console.log(`🌐 CORS enabled for: ${corsOptions.origin.join(', ')}`);
});
