const axios = require('axios');

async function testTrendingTopics() {
    try {
        console.log('🧪 Testing trending topics API...');
        const response = await axios.get('http://localhost:5000/api/home/<USER>');
        
        console.log('✅ API Response Status:', response.status);
        console.log('✅ API Response Data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success && response.data.data) {
            console.log(`📊 Found ${response.data.data.length} trending topics`);
            response.data.data.forEach((topic, index) => {
                console.log(`${index + 1}. ${topic.name} - ${topic.postCount || 0} bài viết (trending: ${topic.trending || false})`);
            });
        }
    } catch (error) {
        console.error('❌ API Error:', error.message);
        if (error.response) {
            console.error('❌ Response Status:', error.response.status);
            console.error('❌ Response Data:', error.response.data);
        }
    }
}

testTrendingTopics();
