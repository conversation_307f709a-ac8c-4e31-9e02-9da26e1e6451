// File: backend/debug-post-count.js
// Script để debug và kiểm tra số lượng bài viết thực tế

const mongoose = require('mongoose');
const Topic = require('./models/Topic');
const Post = require('./models/Post');
require('dotenv').config();

// Kết nối database
async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

// Debug thông tin collections
async function debugCollections() {
    try {
        console.log('\n🔍 Kiểm tra thông tin collections...');

        // Lấy danh sách collections
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('\n📋 Danh sách collections:');
        collections.forEach(col => {
            console.log(`  - ${col.name}`);
        });

        // Kiểm tra model names
        console.log('\n🏷️  Model names:');
        console.log(`  - Topic model collection: ${Topic.collection.name}`);
        console.log(`  - Post model collection: ${Post.collection.name}`);

    } catch (error) {
        console.error('❌ Lỗi khi debug collections:', error);
    }
}

// Kiểm tra chủ đề "Khoa học máy tính"
async function debugSpecificTopic() {
    try {
        console.log('\n🎯 Tìm chủ đề "Khoa học máy tính"...');

        // Tìm chủ đề với tên chứa "Khoa học máy tính"
        const topics = await Topic.find({
            name: { $regex: 'Khoa học máy tính', $options: 'i' }
        });

        if (topics.length === 0) {
            console.log('❌ Không tìm thấy chủ đề "Khoa học máy tính"');

            // Hiển thị tất cả chủ đề để tham khảo
            const allTopics = await Topic.find({}).select('name _id');
            console.log('\n📋 Tất cả chủ đề hiện có:');
            allTopics.forEach(topic => {
                console.log(`  - ${topic.name} (ID: ${topic._id})`);
            });
            return;
        }

        for (const topic of topics) {
            console.log(`\n📌 Chủ đề: ${topic.name}`);
            console.log(`   ID: ${topic._id}`);
            console.log(`   PostCount trong DB: ${topic.postCount || 0}`);

            // Đếm tất cả bài viết thuộc chủ đề này
            const allPosts = await Post.find({ topicId: topic._id });
            console.log(`   Tổng số bài viết thực tế: ${allPosts.length}`);

            // Phân tích theo status
            const statusCounts = {};
            allPosts.forEach(post => {
                const status = post.status || 'undefined';
                statusCounts[status] = (statusCounts[status] || 0) + 1;
            });

            console.log('   📊 Phân tích theo status:');
            Object.entries(statusCounts).forEach(([status, count]) => {
                console.log(`     - ${status}: ${count} bài viết`);
            });

            // Kiểm tra các điều kiện filter khác nhau
            const publishedCount = await Post.countDocuments({
                topicId: topic._id,
                status: 'published'
            });

            const pendingCount = await Post.countDocuments({
                topicId: topic._id,
                status: 'pending'
            });

            const draftCount = await Post.countDocuments({
                topicId: topic._id,
                status: 'draft'
            });

            const activeCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $in: ['published', 'pending', 'draft'] }
            });

            const notDeletedCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $ne: 'deleted' }
            });

            console.log('   🔢 Số lượng theo điều kiện filter:');
            console.log(`     - Published: ${publishedCount}`);
            console.log(`     - Pending: ${pendingCount}`);
            console.log(`     - Draft: ${draftCount}`);
            console.log(`     - Active (published + pending + draft): ${activeCount}`);
            console.log(`     - Not deleted: ${notDeletedCount}`);

            // Hiển thị một vài bài viết mẫu
            console.log('\n📝 Một vài bài viết mẫu:');
            const samplePosts = allPosts.slice(0, 3);
            samplePosts.forEach((post, index) => {
                console.log(`     ${index + 1}. "${post.title}" - Status: ${post.status} - ID: ${post._id}`);
            });
        }

    } catch (error) {
        console.error('❌ Lỗi khi debug chủ đề cụ thể:', error);
    }
}

// Kiểm tra tất cả chủ đề và số lượng bài viết
async function debugAllTopics() {
    try {
        console.log('\n📊 Kiểm tra tất cả chủ đề...');

        const topics = await Topic.find({}).select('name postCount _id');

        console.log(`\n📋 Tìm thấy ${topics.length} chủ đề:`);

        for (const topic of topics) {
            const actualCount = await Post.countDocuments({ topicId: topic._id });
            const activeCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $in: ['published', 'pending', 'draft'] }
            });
            const notDeletedCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $ne: 'deleted' }
            });

            const storedCount = topic.postCount || 0;
            const mismatch = actualCount !== storedCount;

            console.log(`\n  📌 ${topic.name}`);
            console.log(`     Stored: ${storedCount} | Actual: ${actualCount} | Active: ${activeCount} | Not deleted: ${notDeletedCount} ${mismatch ? '❌ MISMATCH' : '✅'}`);

            // Kiểm tra ngày tạo
            const fullTopic = await Topic.findById(topic._id);
            if (fullTopic.createdAt) {
                console.log(`     📅 CreatedAt: ${fullTopic.createdAt.toLocaleString('vi-VN')}`);
            } else {
                console.log(`     📅 CreatedAt: ❌ MISSING - sẽ được tạo từ ObjectId`);
            }
        }

    } catch (error) {
        console.error('❌ Lỗi khi debug tất cả chủ đề:', error);
    }
}

// Sửa lỗi postCount cho tất cả chủ đề
async function fixAllPostCounts() {
    try {
        console.log('\n🔧 Sửa lỗi postCount cho tất cả chủ đề...');

        const topics = await Topic.find({});
        let fixedCount = 0;

        for (const topic of topics) {
            // Đếm tất cả bài viết không bị xóa
            const actualCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $ne: 'deleted' }
            });

            if (topic.postCount !== actualCount) {
                await Topic.findByIdAndUpdate(topic._id, { postCount: actualCount });
                console.log(`  ✅ ${topic.name}: ${topic.postCount || 0} → ${actualCount}`);
                fixedCount++;
            }
        }

        console.log(`\n🎉 Đã sửa ${fixedCount} chủ đề`);

    } catch (error) {
        console.error('❌ Lỗi khi sửa postCount:', error);
    }
}

// Hàm chính
async function main() {
    await connectDB();

    const args = process.argv.slice(2);

    if (args.includes('--debug-collections')) {
        await debugCollections();
    } else if (args.includes('--debug-specific')) {
        await debugSpecificTopic();
    } else if (args.includes('--debug-all')) {
        await debugAllTopics();
    } else if (args.includes('--fix')) {
        await fixAllPostCounts();
    } else {
        console.log('\n🚀 Chạy tất cả debug...');
        await debugCollections();
        await debugSpecificTopic();
        await debugAllTopics();

        console.log('\n❓ Bạn có muốn sửa lỗi postCount không? (y/N)');
        // Trong môi trường thực tế, bạn có thể thêm readline để nhận input
        // Ở đây chúng ta sẽ tự động sửa
        await fixAllPostCounts();
    }

    console.log('\n✨ Hoàn thành!');
    process.exit(0);
}

// Chạy script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    debugCollections,
    debugSpecificTopic,
    debugAllTopics,
    fixAllPostCounts
};
