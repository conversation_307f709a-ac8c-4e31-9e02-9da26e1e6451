const mongoose = require('mongoose');
const Notification = require('./models/Notification');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/dien_dan_TVU', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const checkNotifications = async () => {
    try {
        console.log('🔍 Checking recent notifications...');

        // Get latest 10 notifications
        const notifications = await Notification.find()
            .sort({ createdAt: -1 })
            .limit(10);

        console.log(`📋 Found ${notifications.length} recent notifications:`);
        console.log('');

        notifications.forEach((notification, index) => {
            console.log(`${index + 1}. ${notification.type}`);
            console.log(`   Title: ${notification.title}`);
            console.log(`   Message: ${notification.message}`);
            console.log(`   ActionURL: ${notification.actionUrl || 'No actionUrl'}`);
            console.log(`   Recipient: ${notification.recipient || 'Unknown'}`);
            console.log(`   Created: ${notification.createdAt}`);
            console.log('   ---');
        });

        // Check for old format URLs
        const oldFormatNotifications = await Notification.find({
            actionUrl: { $regex: '/post-detail' }
        }).countDocuments();

        const newFormatNotifications = await Notification.find({
            actionUrl: { $regex: '/posts/detail' }
        }).countDocuments();

        console.log(`📊 URL Format Statistics:`);
        console.log(`   Old format (/post-detail): ${oldFormatNotifications}`);
        console.log(`   New format (/posts/detail): ${newFormatNotifications}`);

        process.exit(0);
    } catch (error) {
        console.error('❌ Error checking notifications:', error);
        process.exit(1);
    }
};

checkNotifications();
