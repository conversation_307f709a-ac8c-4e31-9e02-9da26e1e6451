const mongoose = require('mongoose');
const Notification = require('./models/Notification');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/dien_dan_TVU', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const fixOldNotifications = async () => {
    try {
        console.log('🔧 Fixing old notification URLs...');
        
        // Find notifications with old URL format
        const oldNotifications = await Notification.find({
            actionUrl: { $regex: '/post-detail' }
        });
        
        console.log(`📋 Found ${oldNotifications.length} notifications with old URL format`);
        
        let fixedCount = 0;
        
        for (const notification of oldNotifications) {
            const oldUrl = notification.actionUrl;
            
            if (oldUrl) {
                // Convert old URL to new format
                let newUrl = oldUrl.replace('/post-detail', '/posts/detail');
                
                // If URL doesn't have topicId, we need to add it
                // For now, we'll just update the path
                if (!newUrl.includes('topicId=')) {
                    // Extract postId from URL
                    const postIdMatch = newUrl.match(/postId=([^&#]+)/);
                    if (postIdMatch) {
                        const postId = postIdMatch[1];
                        // Add a default topicId (you might want to fetch the actual topicId from the post)
                        newUrl = newUrl.replace(`postId=${postId}`, `topicId=6814aecb2238577c20bb8ca4&postId=${postId}`);
                    }
                }
                
                // Update the notification
                await Notification.findByIdAndUpdate(notification._id, {
                    actionUrl: newUrl
                });
                
                console.log(`✅ Fixed: ${oldUrl} → ${newUrl}`);
                fixedCount++;
            }
        }
        
        console.log(`🎉 Fixed ${fixedCount} notifications!`);
        
        // Check results
        const remainingOld = await Notification.find({
            actionUrl: { $regex: '/post-detail' }
        }).countDocuments();
        
        const totalNew = await Notification.find({
            actionUrl: { $regex: '/posts/detail' }
        }).countDocuments();
        
        console.log(`📊 Final Statistics:`);
        console.log(`   Old format remaining: ${remainingOld}`);
        console.log(`   New format total: ${totalNew}`);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error fixing notifications:', error);
        process.exit(1);
    }
};

fixOldNotifications();
