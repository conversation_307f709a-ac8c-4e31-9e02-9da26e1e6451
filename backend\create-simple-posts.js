// Simple script to create posts
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Post = require('./models/Post');
const Topic = require('./models/Topic');
const User = require('./models/User');

async function createPosts() {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to MongoDB');

        // Get first topic and user
        const topic = await Topic.findOne({});
        const user = await User.findOne({});

        if (!topic) {
            console.log('❌ No topics found');
            return;
        }

        if (!user) {
            console.log('❌ No users found');
            return;
        }

        console.log(`📝 Creating posts for topic: ${topic.name}`);
        console.log(`👤 Author: ${user.fullName}`);

        // Create 3 simple posts
        const posts = [
            {
                title: "Bài viết test 1 - Hướng dẫn học lập trình",
                content: "<h2>Nội dung bài viết 1</h2><p><PERSON><PERSON>y là nội dung chi tiết của bài viết về lập trình. Bài viết này sẽ hướng dẫn các bạn những kiến thức cơ bản.</p>",
                authorId: user._id,
                topicId: topic._id,
                tags: ["lập trình", "tutorial"],
                views: 100,
                commentCount: 5,
                likeCount: 10
            },
            {
                title: "Bài viết test 2 - Kinh nghiệm học tập",
                content: "<h2>Nội dung bài viết 2</h2><p>Chia sẻ kinh nghiệm học tập hiệu quả cho sinh viên. Những phương pháp này đã được nhiều người áp dụng thành công.</p>",
                authorId: user._id,
                topicId: topic._id,
                tags: ["học tập", "kinh nghiệm"],
                views: 150,
                commentCount: 8,
                likeCount: 15
            },
            {
                title: "Bài viết test 3 - Thảo luận về công nghệ",
                content: "<h2>Nội dung bài viết 3</h2><p>Thảo luận về những xu hướng công nghệ mới nhất. Cùng nhau tìm hiểu và chia sẻ quan điểm.</p>",
                authorId: user._id,
                topicId: topic._id,
                tags: ["công nghệ", "thảo luận"],
                views: 200,
                commentCount: 12,
                likeCount: 20
            }
        ];

        // Delete existing posts
        await Post.deleteMany({});
        console.log('🗑️ Deleted existing posts');

        // Create new posts
        for (const postData of posts) {
            const post = new Post(postData);
            await post.save();
            console.log(`✅ Created: ${post.title}`);
        }

        console.log('🎉 All posts created successfully!');

        // Verify
        const totalPosts = await Post.countDocuments();
        const topicPosts = await Post.countDocuments({ topicId: topic._id });
        
        console.log(`📊 Total posts: ${totalPosts}`);
        console.log(`📊 Posts for "${topic.name}": ${topicPosts}`);
        console.log(`🔗 Test URL: http://localhost:5000/api/posts/topic-details/${topic._id}`);

        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

createPosts();
