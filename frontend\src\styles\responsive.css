/* Responsive design utilities */

/* Mobile First Approach */

/* Extra Small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
    .hero-title {
        font-size: 2rem !important;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1rem !important;
        padding: 0 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .featured-post-card {
        margin-bottom: 1.5rem;
    }
    
    .search-field {
        margin: 0 1rem;
    }
    
    .section-padding {
        padding: 2rem 0 !important;
    }
    
    .container-mobile {
        padding: 0 1rem !important;
    }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
    .hero-title {
        font-size: 2.5rem !important;
    }
    
    .hero-subtitle {
        font-size: 1.1rem !important;
    }
    
    .stats-grid {
        gap: 1rem;
    }
    
    .section-padding {
        padding: 3rem 0 !important;
    }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
    .hero-title {
        font-size: 3rem !important;
    }
    
    .hero-subtitle {
        font-size: 1.2rem !important;
    }
    
    .featured-posts-grid {
        gap: 2rem;
    }
    
    .trending-topics-grid {
        gap: 1.5rem;
    }
    
    .section-padding {
        padding: 4rem 0 !important;
    }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
    .hero-title {
        font-size: 3.5rem !important;
    }
    
    .hero-subtitle {
        font-size: 1.3rem !important;
    }
    
    .container-large {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .section-padding {
        padding: 5rem 0 !important;
    }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
    .hero-title {
        font-size: 4rem !important;
    }
    
    .container-xl {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .section-padding {
        padding: 6rem 0 !important;
    }
}

/* Utility classes for responsive design */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

@media only screen and (max-width: 768px) {
    .hide-mobile {
        display: none !important;
    }
    
    .show-mobile {
        display: block !important;
    }
    
    .show-mobile-flex {
        display: flex !important;
    }
    
    .show-mobile-inline {
        display: inline !important;
    }
}

/* Text responsive utilities */
.text-responsive-xl {
    font-size: clamp(2rem, 5vw, 4rem);
}

.text-responsive-lg {
    font-size: clamp(1.5rem, 4vw, 3rem);
}

.text-responsive-md {
    font-size: clamp(1.2rem, 3vw, 2rem);
}

.text-responsive-sm {
    font-size: clamp(1rem, 2vw, 1.5rem);
}

/* Spacing responsive utilities */
.spacing-responsive-sm {
    margin: clamp(0.5rem, 2vw, 1rem);
}

.spacing-responsive-md {
    margin: clamp(1rem, 3vw, 2rem);
}

.spacing-responsive-lg {
    margin: clamp(1.5rem, 4vw, 3rem);
}

.spacing-responsive-xl {
    margin: clamp(2rem, 5vw, 4rem);
}

/* Grid responsive utilities */
.grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media only screen and (min-width: 600px) {
    .grid-responsive-sm-2 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (min-width: 768px) {
    .grid-responsive-md-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-responsive-md-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media only screen and (min-width: 992px) {
    .grid-responsive-lg-5 {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .grid-responsive-lg-6 {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Flexbox responsive utilities */
.flex-responsive {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

@media only screen and (min-width: 768px) {
    .flex-responsive-md-row {
        flex-direction: row;
    }
}

/* Card responsive utilities */
.card-responsive {
    padding: 1rem;
    border-radius: 0.5rem;
}

@media only screen and (min-width: 768px) {
    .card-responsive {
        padding: 1.5rem;
        border-radius: 0.75rem;
    }
}

@media only screen and (min-width: 992px) {
    .card-responsive {
        padding: 2rem;
        border-radius: 1rem;
    }
}

/* Button responsive utilities */
.button-responsive {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
}

@media only screen and (min-width: 768px) {
    .button-responsive {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}

@media only screen and (min-width: 992px) {
    .button-responsive {
        padding: 1.25rem 2.5rem;
        font-size: 1.1rem;
    }
}

/* Image responsive utilities */
.image-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.image-responsive-square {
    aspect-ratio: 1;
}

.image-responsive-landscape {
    aspect-ratio: 16/9;
}

.image-responsive-portrait {
    aspect-ratio: 3/4;
}

/* Container responsive utilities */
.container-responsive {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media only screen and (min-width: 768px) {
    .container-responsive {
        padding: 0 2rem;
    }
}

@media only screen and (min-width: 992px) {
    .container-responsive {
        padding: 0 3rem;
    }
}

/* Accessibility improvements for mobile */
@media only screen and (max-width: 768px) {
    .touch-target {
        min-height: 44px;
        min-width: 44px;
    }
    
    .clickable-area {
        padding: 0.75rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
}
