// Create posts for all topics
const mongoose = require('mongoose');
require('dotenv').config();

const Post = require('./models/Post');
const Topic = require('./models/Topic');
const User = require('./models/User');

async function createPostsForAllTopics() {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to MongoDB');

        // Get all topics and users
        const topics = await Topic.find({});
        const users = await User.find({});

        if (topics.length === 0) {
            console.log('❌ No topics found');
            return;
        }

        if (users.length === 0) {
            console.log('❌ No users found');
            return;
        }

        console.log(`📊 Found ${topics.length} topics and ${users.length} users`);

        // Sample posts for each topic
        const postsData = {
            "Khoa học máy tính": [
                {
                    title: "Hướng dẫn học Python từ cơ bản đến nâng cao",
                    content: "<h2>Python - Ngôn ngữ lập trình thân thiện</h2><p>Python là một trong những ngôn ngữ lập trình phổ biến nhất hiện nay. Bài viết này sẽ hướng dẫn bạn từ những kiến thức cơ bản nhất.</p><h3>Tại sao nên học Python?</h3><ul><li>Cú pháp đơn giản, dễ hiểu</li><li>Thư viện phong phú</li><li>Cộng đồng lớn</li><li>Ứng dụng rộng rãi</li></ul>",
                    tags: ["python", "lập trình", "tutorial"]
                },
                {
                    title: "So sánh React vs Vue.js vs Angular",
                    content: "<h2>Framework JavaScript nào phù hợp với bạn?</h2><p>Trong thế giới frontend development, có 3 framework chính đang thống trị thị trường.</p><h3>React</h3><p>✅ Cộng đồng lớn<br>✅ Ecosystem phong phú<br>❌ Learning curve cao</p><h3>Vue.js</h3><p>✅ Dễ học<br>✅ Documentation tốt<br>❌ Cộng đồng nhỏ hơn</p>",
                    tags: ["javascript", "react", "vue", "angular"]
                },
                {
                    title: "Kinh nghiệm phỏng vấn Software Engineer",
                    content: "<h2>Chia sẻ kinh nghiệm phỏng vấn</h2><p>Sau 5 lần phỏng vấn tại các công ty công nghệ, mình muốn chia sẻ một số kinh nghiệm.</p><h3>Chuẩn bị</h3><ol><li>Ôn tập Data Structures & Algorithms</li><li>Luyện tập trên LeetCode</li><li>Chuẩn bị câu hỏi về công ty</li></ol>",
                    tags: ["phỏng vấn", "career", "tips"]
                }
            ],
            "Học tập": [
                {
                    title: "Phương pháp Pomodoro - Quản lý thời gian hiệu quả",
                    content: "<h2>Kỹ thuật Pomodoro</h2><p>Pomodoro là phương pháp quản lý thời gian được nhiều người áp dụng thành công.</p><h3>Cách thực hiện</h3><ol><li>Chọn nhiệm vụ</li><li>Đặt timer 25 phút</li><li>Tập trung làm việc</li><li>Nghỉ 5 phút</li><li>Lặp lại</li></ol>",
                    tags: ["pomodoro", "quản lý thời gian", "productivity"]
                },
                {
                    title: "Cách ghi chú hiệu quả với Cornell Notes",
                    content: "<h2>Cornell Note-Taking System</h2><p>Phương pháp ghi chú được phát triển tại Đại học Cornell, giúp tăng hiệu quả học tập.</p><h3>Cấu trúc</h3><ul><li>Cue Column: Từ khóa, câu hỏi</li><li>Note-taking Area: Nội dung chính</li><li>Summary: Tóm tắt</li></ul>",
                    tags: ["ghi chú", "cornell notes", "study tips"]
                }
            ],
            "Sinh viên": [
                {
                    title: "Kinh nghiệm sống xa nhà cho tân sinh viên",
                    content: "<h2>Lần đầu sống xa nhà?</h2><p>Đây là những điều bạn cần biết khi bước vào đời sống sinh viên.</p><h3>Về chỗ ở</h3><ul><li>Ký túc xá: Rẻ, an toàn</li><li>Nhà trọ: Tự do hơn</li><li>Thuê chung: Tiết kiệm</li></ul>",
                    tags: ["sinh viên", "sống xa nhà", "kinh nghiệm"]
                },
                {
                    title: "Top 10 ứng dụng không thể thiếu cho sinh viên",
                    content: "<h2>Apps hữu ích cho sinh viên</h2><h3>Học tập</h3><ol><li>Notion - Ghi chú all-in-one</li><li>Anki - Flashcards thông minh</li><li>Forest - Tập trung học tập</li></ol><h3>Tài chính</h3><ol><li>Momo - Thanh toán</li><li>Money Lover - Quản lý chi tiêu</li></ol>",
                    tags: ["apps", "sinh viên", "công nghệ"]
                }
            ],
            "Công nghệ": [
                {
                    title: "Xu hướng công nghệ 2024: AI và Machine Learning",
                    content: "<h2>AI đang thay đổi thế giới</h2><p>Trí tuệ nhân tạo không còn là khái niệm xa vời mà đã trở thành hiện thực.</p><h3>Các lĩnh vực ứng dụng</h3><ul><li>Healthcare</li><li>Education</li><li>Finance</li><li>Transportation</li></ul>",
                    tags: ["AI", "machine learning", "công nghệ", "2024"]
                },
                {
                    title: "Blockchain và tương lai của tiền điện tử",
                    content: "<h2>Blockchain beyond Bitcoin</h2><p>Công nghệ blockchain không chỉ dừng lại ở tiền điện tử mà còn có nhiều ứng dụng khác.</p><h3>Ứng dụng</h3><ul><li>Smart contracts</li><li>Supply chain</li><li>Digital identity</li></ul>",
                    tags: ["blockchain", "cryptocurrency", "fintech"]
                }
            ],
            "Giải trí": [
                {
                    title: "Top 5 địa điểm check-in đẹp nhất Trà Vinh",
                    content: "<h2>Khám phá Trà Vinh</h2><p>Trà Vinh không chỉ có trường đại học mà còn có nhiều địa điểm đẹp để check-in.</p><h3>Danh sách</h3><ol><li>Chùa Âng</li><li>Ao Bà Om</li><li>Bảo tàng Khmer</li><li>Chợ đêm Trà Vinh</li><li>Cầu Cổ Chiên</li></ol>",
                    tags: ["trà vinh", "du lịch", "check-in"]
                },
                {
                    title: "Review phim hay tháng này",
                    content: "<h2>Những bộ phim đáng xem</h2><p>Tháng này có nhiều bộ phim hay được ra mắt, cùng xem review nhé!</p><h3>Top picks</h3><ul><li>Dune: Part Two - Sci-fi epic</li><li>Oppenheimer - Historical drama</li><li>Spider-Verse - Animation</li></ul>",
                    tags: ["phim", "review", "giải trí"]
                }
            ]
        };

        // Delete existing posts
        await Post.deleteMany({});
        console.log('🗑️ Deleted existing posts');

        let totalCreated = 0;

        // Create posts for each topic
        for (const topic of topics) {
            const topicPosts = postsData[topic.name] || [];
            
            if (topicPosts.length === 0) {
                console.log(`⚠️ No posts data for topic: ${topic.name}`);
                continue;
            }

            console.log(`\n📝 Creating posts for: ${topic.name}`);

            for (const postData of topicPosts) {
                // Random author
                const author = users[Math.floor(Math.random() * users.length)];
                
                const post = new Post({
                    title: postData.title,
                    content: postData.content,
                    authorId: author._id,
                    topicId: topic._id,
                    tags: postData.tags,
                    views: Math.floor(Math.random() * 500) + 50,
                    commentCount: Math.floor(Math.random() * 20),
                    likeCount: Math.floor(Math.random() * 50),
                    ratingCount: Math.floor(Math.random() * 10),
                    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
                });

                await post.save();
                console.log(`  ✅ ${postData.title}`);
                totalCreated++;
            }
        }

        console.log(`\n🎉 Created ${totalCreated} posts successfully!`);

        // Show statistics
        console.log('\n📊 Posts by topic:');
        for (const topic of topics) {
            const count = await Post.countDocuments({ topicId: topic._id });
            console.log(`  - ${topic.name}: ${count} posts`);
        }

        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

createPostsForAllTopics();
