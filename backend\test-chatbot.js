// File: backend/test-chatbot.js
// Script test cho chatbot APIs

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = 'http://localhost:5000/api';

// Thông tin admin để test
const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'admin123'
};

let authToken = '';

// Tạo axios instance
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000
});

// Interceptor để thêm token
api.interceptors.request.use(config => {
    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
});

// Đăng nhập admin
async function loginAdmin() {
    console.log('\n=== Đăng nhập Admin ===');
    try {
        const response = await api.post('/auth/login', ADMIN_CREDENTIALS);
        if (response.data.success && response.data.token) {
            authToken = response.data.token;
            console.log('✅ Đăng nhập thành công');
            return true;
        } else {
            console.log('❌ Đăng nhập thất bại:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Lỗi đăng nhập:', error.response?.data?.message || error.message);
        return false;
    }
}

// Test get intents
async function testGetIntents() {
    console.log('\n=== Test: Get Intents ===');
    try {
        const response = await api.get('/admin/chatbot/intents');
        console.log('✅ Success:', {
            totalIntents: response.data.data.pagination.totalIntents,
            currentPage: response.data.data.pagination.currentPage,
            intentsCount: response.data.data.intents.length
        });
        return response.data.data.intents;
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
        return [];
    }
}

// Test create intent
async function testCreateIntent() {
    console.log('\n=== Test: Create Intent ===');
    try {
        const intentData = {
            name: 'test_greeting',
            displayName: 'Test Greeting',
            description: 'Intent test cho chào hỏi',
            category: 'greeting',
            trainingPhrases: [
                { text: 'xin chào' },
                { text: 'hello' },
                { text: 'chào bạn' }
            ],
            responses: [
                { type: 'text', text: 'Xin chào! Tôi có thể giúp gì cho bạn?' },
                { type: 'text', text: 'Chào bạn! Bạn cần hỗ trợ gì không?' }
            ],
            tags: ['test', 'greeting']
        };

        const response = await api.post('/admin/chatbot/intents', intentData);
        console.log('✅ Success:', response.data.message);
        return response.data.data;
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
        return null;
    }
}

// Test update intent
async function testUpdateIntent(intentId) {
    console.log('\n=== Test: Update Intent ===');
    try {
        const updateData = {
            description: 'Intent test đã được cập nhật',
            trainingPhrases: [
                { text: 'xin chào' },
                { text: 'hello' },
                { text: 'chào bạn' },
                { text: 'hi' }
            ]
        };

        const response = await api.put(`/admin/chatbot/intents/${intentId}`, updateData);
        console.log('✅ Success:', response.data.message);
        return response.data.data;
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
        return null;
    }
}

// Test get intent by id
async function testGetIntentById(intentId) {
    console.log('\n=== Test: Get Intent By ID ===');
    try {
        const response = await api.get(`/admin/chatbot/intents/${intentId}`);
        console.log('✅ Success:', {
            name: response.data.data.name,
            displayName: response.data.data.displayName,
            category: response.data.data.category,
            trainingPhrasesCount: response.data.data.trainingPhrases.length,
            responsesCount: response.data.data.responses.length
        });
        return response.data.data;
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
        return null;
    }
}

// Test add training phrase
async function testAddTrainingPhrase(intentId) {
    console.log('\n=== Test: Add Training Phrase ===');
    try {
        const response = await api.post(`/admin/chatbot/intents/${intentId}/training-phrases`, {
            text: 'chào buổi sáng'
        });
        console.log('✅ Success:', response.data.message);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test add response
async function testAddResponse(intentId) {
    console.log('\n=== Test: Add Response ===');
    try {
        const response = await api.post(`/admin/chatbot/intents/${intentId}/responses`, {
            type: 'text',
            text: 'Chào buổi sáng! Chúc bạn một ngày tốt lành!'
        });
        console.log('✅ Success:', response.data.message);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test sync with Dialogflow
async function testSyncDialogflow() {
    console.log('\n=== Test: Sync with Dialogflow ===');
    try {
        const response = await api.post('/admin/chatbot/sync');
        console.log('✅ Success:', response.data.message);
        console.log('Sync results:', {
            syncedCount: response.data.data.syncedCount,
            errorCount: response.data.data.errorCount
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test train agent
async function testTrainAgent() {
    console.log('\n=== Test: Train Agent ===');
    try {
        const response = await api.post('/admin/chatbot/train');
        console.log('✅ Success:', response.data.message);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test get conversations
async function testGetConversations() {
    console.log('\n=== Test: Get Conversations ===');
    try {
        const response = await api.get('/admin/chatbot/conversations');
        console.log('✅ Success:', {
            totalConversations: response.data.data.pagination.totalConversations,
            currentPage: response.data.data.pagination.currentPage,
            conversationsCount: response.data.data.conversations.length
        });
        return response.data.data.conversations;
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
        return [];
    }
}

// Test chatbot analytics
async function testChatbotAnalytics() {
    console.log('\n=== Test: Chatbot Analytics ===');
    try {
        const response = await api.get('/admin/chatbot/analytics/overview');
        console.log('✅ Success:', {
            totalIntents: response.data.data.overview.totalIntents,
            activeIntents: response.data.data.overview.activeIntents,
            totalConversations: response.data.data.overview.totalConversations,
            needsReviewCount: response.data.data.overview.needsReviewCount
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test intent analytics
async function testIntentAnalytics() {
    console.log('\n=== Test: Intent Analytics ===');
    try {
        const response = await api.get('/admin/chatbot/analytics/intents');
        console.log('✅ Success:', {
            topIntentsCount: response.data.data.topIntents.length,
            lowSuccessRateIntentsCount: response.data.data.lowSuccessRateIntents.length,
            unsyncedIntentsCount: response.data.data.unsyncedIntents.length
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test conversation analytics
async function testConversationAnalytics() {
    console.log('\n=== Test: Conversation Analytics ===');
    try {
        const response = await api.get('/admin/chatbot/analytics/conversations');
        console.log('✅ Success:', {
            conversationTrendCount: response.data.data.conversationTrend.length,
            conversationByHourCount: response.data.data.conversationByHour.length,
            conversationByDeviceCount: response.data.data.conversationByDevice.length
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test delete intent
async function testDeleteIntent(intentId) {
    console.log('\n=== Test: Delete Intent ===');
    try {
        const response = await api.delete(`/admin/chatbot/intents/${intentId}`);
        console.log('✅ Success:', response.data.message);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Hàm chính
async function main() {
    console.log('🚀 Bắt đầu test Chatbot APIs...');
    
    // Đăng nhập
    const loginSuccess = await loginAdmin();
    if (!loginSuccess) {
        console.log('❌ Không thể đăng nhập. Dừng test.');
        return;
    }
    
    const args = process.argv.slice(2);
    
    if (args.includes('--intents-only')) {
        const intents = await testGetIntents();
        if (intents.length > 0) {
            await testGetIntentById(intents[0]._id);
        }
    } else if (args.includes('--create-only')) {
        const newIntent = await testCreateIntent();
        if (newIntent) {
            await testAddTrainingPhrase(newIntent._id);
            await testAddResponse(newIntent._id);
            await testUpdateIntent(newIntent._id);
        }
    } else if (args.includes('--analytics-only')) {
        await testChatbotAnalytics();
        await testIntentAnalytics();
        await testConversationAnalytics();
    } else if (args.includes('--conversations-only')) {
        await testGetConversations();
    } else if (args.includes('--sync-only')) {
        await testSyncDialogflow();
        await testTrainAgent();
    } else {
        // Test tất cả
        const intents = await testGetIntents();
        
        // Test create và update
        const newIntent = await testCreateIntent();
        if (newIntent) {
            await testGetIntentById(newIntent._id);
            await testAddTrainingPhrase(newIntent._id);
            await testAddResponse(newIntent._id);
            await testUpdateIntent(newIntent._id);
        }
        
        // Test conversations
        await testGetConversations();
        
        // Test analytics
        await testChatbotAnalytics();
        await testIntentAnalytics();
        await testConversationAnalytics();
        
        // Test sync (comment out nếu chưa config Dialogflow)
        // await testSyncDialogflow();
        // await testTrainAgent();
        
        // Cleanup - xóa intent test
        if (newIntent) {
            await testDeleteIntent(newIntent._id);
        }
    }
    
    console.log('\n✨ Hoàn thành test Chatbot APIs!');
}

// Chạy test
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    testGetIntents,
    testCreateIntent,
    testUpdateIntent,
    testGetIntentById,
    testAddTrainingPhrase,
    testAddResponse,
    testSyncDialogflow,
    testTrainAgent,
    testGetConversations,
    testChatbotAnalytics,
    testIntentAnalytics,
    testConversationAnalytics,
    testDeleteIntent
};
