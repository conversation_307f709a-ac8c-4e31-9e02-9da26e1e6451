// File: backend/check-dialogflow-config.js
// Script để kiểm tra cấu hình Dialogflow

require('dotenv').config();
const fs = require('fs');
const path = require('path');

function checkDialogflowConfig() {
    console.log('🔍 Kiểm tra cấu hình Dialogflow...\n');
    
    let hasErrors = false;
    
    // 1. Kiểm tra DIALOGFLOW_PROJECT_ID
    console.log('1. Kiểm tra DIALOGFLOW_PROJECT_ID:');
    if (process.env.DIALOGFLOW_PROJECT_ID) {
        console.log(`   ✅ DIALOGFLOW_PROJECT_ID: ${process.env.DIALOGFLOW_PROJECT_ID}`);
    } else {
        console.log('   ❌ DIALOGFLOW_PROJECT_ID không được thiết lập');
        console.log('   💡 Thêm vào file .env: DIALOGFLOW_PROJECT_ID=your-project-id');
        hasErrors = true;
    }
    
    // 2. <PERSON><PERSON><PERSON> tra GOOGLE_APPLICATION_CREDENTIALS
    console.log('\n2. Kiểm tra GOOGLE_APPLICATION_CREDENTIALS:');
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        console.log(`   ✅ GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
        
        // Kiểm tra file có tồn tại không
        const credentialsPath = path.resolve(process.env.GOOGLE_APPLICATION_CREDENTIALS);
        if (fs.existsSync(credentialsPath)) {
            console.log('   ✅ File credentials tồn tại');
            
            // Kiểm tra nội dung file
            try {
                const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
                if (credentials.type === 'service_account') {
                    console.log('   ✅ File credentials hợp lệ (service account)');
                    console.log(`   📧 Client email: ${credentials.client_email}`);
                    console.log(`   🆔 Project ID trong credentials: ${credentials.project_id}`);
                    
                    // So sánh project ID
                    if (credentials.project_id !== process.env.DIALOGFLOW_PROJECT_ID) {
                        console.log('   ⚠️  Project ID trong credentials khác với DIALOGFLOW_PROJECT_ID');
                        console.log(`      Credentials: ${credentials.project_id}`);
                        console.log(`      Environment: ${process.env.DIALOGFLOW_PROJECT_ID}`);
                    }
                } else {
                    console.log('   ❌ File credentials không phải service account');
                    hasErrors = true;
                }
            } catch (error) {
                console.log('   ❌ Không thể đọc file credentials:', error.message);
                hasErrors = true;
            }
        } else {
            console.log('   ❌ File credentials không tồn tại');
            console.log(`   📁 Đường dẫn: ${credentialsPath}`);
            hasErrors = true;
        }
    } else {
        console.log('   ❌ GOOGLE_APPLICATION_CREDENTIALS không được thiết lập');
        console.log('   💡 Thêm vào file .env: GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json');
        hasErrors = true;
    }
    
    // 3. Kiểm tra DIALOGFLOW_LANGUAGE_CODE
    console.log('\n3. Kiểm tra DIALOGFLOW_LANGUAGE_CODE:');
    const languageCode = process.env.DIALOGFLOW_LANGUAGE_CODE || 'vi';
    console.log(`   ✅ DIALOGFLOW_LANGUAGE_CODE: ${languageCode}`);
    
    // 4. Test kết nối Dialogflow
    console.log('\n4. Test kết nối Dialogflow:');
    try {
        const dialogflowService = require('./services/dialogflowService');
        console.log('   ✅ DialogflowService khởi tạo thành công');
        
        // Test validate config
        const isValid = dialogflowService.validateConfig();
        if (isValid) {
            console.log('   ✅ Cấu hình Dialogflow hợp lệ');
        }
    } catch (error) {
        console.log('   ❌ Lỗi khi khởi tạo DialogflowService:', error.message);
        hasErrors = true;
    }
    
    // 5. Kiểm tra dependencies
    console.log('\n5. Kiểm tra dependencies:');
    try {
        require('@google-cloud/dialogflow');
        console.log('   ✅ @google-cloud/dialogflow đã được cài đặt');
    } catch (error) {
        console.log('   ❌ @google-cloud/dialogflow chưa được cài đặt');
        console.log('   💡 Chạy: npm install @google-cloud/dialogflow');
        hasErrors = true;
    }
    
    try {
        require('uuid');
        console.log('   ✅ uuid đã được cài đặt');
    } catch (error) {
        console.log('   ❌ uuid chưa được cài đặt');
        console.log('   💡 Chạy: npm install uuid');
        hasErrors = true;
    }
    
    // Kết quả
    console.log('\n' + '='.repeat(50));
    if (hasErrors) {
        console.log('❌ Có lỗi trong cấu hình Dialogflow');
        console.log('\n📋 Các bước để sửa lỗi:');
        console.log('1. Tạo Google Cloud Project');
        console.log('2. Enable Dialogflow API');
        console.log('3. Tạo Service Account và download JSON key');
        console.log('4. Thiết lập environment variables trong .env');
        console.log('5. Cài đặt dependencies');
        console.log('\n📖 Xem chi tiết trong ADMIN_CHATBOT_MANAGEMENT_README.md');
        return false;
    } else {
        console.log('✅ Cấu hình Dialogflow hoàn tất!');
        console.log('🚀 Bạn có thể sử dụng các tính năng chatbot');
        return true;
    }
}

// Test kết nối thực tế (optional)
async function testDialogflowConnection() {
    console.log('\n🔗 Test kết nối thực tế với Dialogflow...');
    
    try {
        const dialogflowService = require('./services/dialogflowService');
        
        // Test detect intent
        const sessionId = 'test-session-' + Date.now();
        const result = await dialogflowService.detectIntent(sessionId, 'xin chào');
        
        if (result.success) {
            console.log('✅ Kết nối Dialogflow thành công!');
            console.log(`   Intent: ${result.data.intent.displayName}`);
            console.log(`   Response: ${result.data.fulfillmentText}`);
            console.log(`   Confidence: ${result.data.intent.confidence}`);
            return true;
        } else {
            console.log('❌ Lỗi khi test detect intent:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Lỗi khi test kết nối:', error.message);
        return false;
    }
}

// Hàm chính
async function main() {
    console.log('🤖 Dialogflow Configuration Checker\n');
    
    const configOK = checkDialogflowConfig();
    
    if (configOK && process.argv.includes('--test-connection')) {
        await testDialogflowConnection();
    }
    
    if (!configOK) {
        process.exit(1);
    }
}

// Chạy script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    checkDialogflowConfig,
    testDialogflowConnection
};
