// Check posts by creation time
require('dotenv').config();

async function checkPostsTimeline() {
    try {
        console.log('Checking posts timeline...');
        
        const { MongoClient } = require('mongodb');
        const client = new MongoClient(process.env.MONGO_URI);
        
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db();
        
        // Get all posts with creation dates
        const allPosts = await db.collection('posts').find({}).sort({ createdAt: -1 }).toArray();
        
        console.log(`📊 Total posts found: ${allPosts.length}`);
        
        // Group by date
        const postsByDate = {};
        const today = new Date().toDateString();
        
        allPosts.forEach(post => {
            const date = new Date(post.createdAt).toDateString();
            if (!postsByDate[date]) {
                postsByDate[date] = [];
            }
            postsByDate[date].push(post);
        });
        
        console.log('\n📅 Posts by date:');
        Object.keys(postsByDate).forEach(date => {
            const isToday = date === today;
            const count = postsByDate[date].length;
            console.log(`${isToday ? '🔥 TODAY' : '📅'} ${date}: ${count} posts`);
            
            // Show first few posts for each date
            postsByDate[date].slice(0, 3).forEach((post, index) => {
                console.log(`   ${index + 1}. ${post.title.substring(0, 60)}...`);
            });
            
            if (postsByDate[date].length > 3) {
                console.log(`   ... and ${postsByDate[date].length - 3} more`);
            }
            console.log('');
        });
        
        // Check for duplicate titles
        console.log('🔍 Checking for duplicates...');
        const titleCounts = {};
        allPosts.forEach(post => {
            const title = post.title;
            titleCounts[title] = (titleCounts[title] || 0) + 1;
        });
        
        const duplicates = Object.keys(titleCounts).filter(title => titleCounts[title] > 1);
        if (duplicates.length > 0) {
            console.log('⚠️ Found duplicate titles:');
            duplicates.forEach(title => {
                console.log(`   "${title}" appears ${titleCounts[title]} times`);
            });
        } else {
            console.log('✅ No duplicate titles found');
        }
        
        // Check posts without topics
        const postsWithoutTopic = await db.collection('posts').find({ topicId: { $exists: false } }).toArray();
        if (postsWithoutTopic.length > 0) {
            console.log(`\n⚠️ Found ${postsWithoutTopic.length} posts without topics`);
        }
        
        await client.close();
        console.log('\n✅ Timeline check completed');
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

checkPostsTimeline();
