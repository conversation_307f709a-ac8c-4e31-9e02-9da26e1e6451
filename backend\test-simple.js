// File: backend/test-simple.js
// Test đơn giản để kiểm tra cấu hình

require('dotenv').config();
const mongoose = require('mongoose');

async function testBasic() {
    console.log('🧪 Test cơ bản...\n');
    
    // 1. Test environment variables
    console.log('1. Environment Variables:');
    console.log(`   MONGO_URI: ${process.env.MONGO_URI ? '✅ OK' : '❌ Missing'}`);
    console.log(`   DIALOGFLOW_PROJECT_ID: ${process.env.DIALOGFLOW_PROJECT_ID ? '✅ OK' : '❌ Missing'}`);
    console.log(`   GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS ? '✅ OK' : '❌ Missing'}`);
    
    // 2. Test MongoDB connection
    console.log('\n2. MongoDB Connection:');
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('   ✅ MongoDB connected');
        
        // Test collections
        const ChatbotIntent = require('./models/ChatbotIntent');
        const intentCount = await ChatbotIntent.countDocuments();
        console.log(`   ✅ ChatbotIntent collection: ${intentCount} documents`);
        
        await mongoose.disconnect();
        console.log('   ✅ MongoDB disconnected');
    } catch (error) {
        console.log(`   ❌ MongoDB error: ${error.message}`);
    }
    
    // 3. Test Dialogflow (nếu có cấu hình)
    console.log('\n3. Dialogflow Configuration:');
    if (process.env.DIALOGFLOW_PROJECT_ID && process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        try {
            const DialogflowService = require('./services/dialogflowService');
            const service = DialogflowService.instance();
            console.log('   ✅ DialogflowService initialized');
            
            // Test detect intent
            const result = await service.detectIntent('test-session', 'xin chào');
            if (result.success) {
                console.log('   ✅ Detect intent working');
                console.log(`      Intent: ${result.data.intent.displayName}`);
                console.log(`      Response: ${result.data.fulfillmentText}`);
            } else {
                console.log(`   ❌ Detect intent failed: ${result.error}`);
            }
        } catch (error) {
            console.log(`   ❌ Dialogflow error: ${error.message}`);
        }
    } else {
        console.log('   ⚠️  Dialogflow not configured');
    }
    
    console.log('\n✨ Test hoàn thành!');
}

if (require.main === module) {
    testBasic().catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
