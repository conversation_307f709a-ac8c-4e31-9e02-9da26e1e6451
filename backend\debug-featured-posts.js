// Debug featured posts API
require('dotenv').config();
const mongoose = require('mongoose');
const Post = require('./models/Post');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
};

const debugFeaturedPosts = async () => {
    await connectDB();

    console.log('\n🔍 Debugging Featured Posts...\n');

    try {
        // Check all posts
        const allPosts = await Post.find().select('title featured views commentCount likeCount');
        console.log(`📊 Total posts in database: ${allPosts.length}`);

        // Check featured posts
        const featuredPosts = await Post.find({ featured: true }).select('title featured views commentCount likeCount');
        console.log(`⭐ Featured posts: ${featuredPosts.length}`);

        if (featuredPosts.length > 0) {
            console.log('\n📋 Featured Posts List:');
            featuredPosts.forEach((post, index) => {
                console.log(`${index + 1}. "${post.title}"`);
                console.log(`   Featured: ${post.featured}`);
                console.log(`   Views: ${post.views || 0}`);
                console.log(`   Comments: ${post.commentCount || 0}`);
                console.log(`   Likes: ${post.likeCount || 0}`);
                console.log('   ' + '-'.repeat(40));
            });
        } else {
            console.log('\n❌ No featured posts found!');
        }

        // Check posts with featured field
        const postsWithFeaturedField = await Post.find({ featured: { $exists: true } }).select('title featured');
        console.log(`\n📋 Posts with featured field: ${postsWithFeaturedField.length}`);

        // Show featured status distribution
        const featuredTrue = await Post.countDocuments({ featured: true });
        const featuredFalse = await Post.countDocuments({ featured: false });
        const featuredNull = await Post.countDocuments({ featured: { $exists: false } });

        console.log('\n📊 Featured Status Distribution:');
        console.log(`   featured: true  -> ${featuredTrue} posts`);
        console.log(`   featured: false -> ${featuredFalse} posts`);
        console.log(`   featured: null  -> ${featuredNull} posts`);

        // Test the API aggregation
        console.log('\n🔧 Testing API Aggregation...');

        const featuredPostsAPI = await Post.aggregate([
            {
                $lookup: {
                    from: 'users',
                    localField: 'authorId',
                    foreignField: '_id',
                    as: 'authorInfo'
                }
            },
            {
                $lookup: {
                    from: 'topics',
                    localField: 'topicId',
                    foreignField: '_id',
                    as: 'topicInfo'
                }
            },
            {
                $addFields: {
                    authorInfo: { $arrayElemAt: ['$authorInfo', 0] },
                    topicInfo: { $arrayElemAt: ['$topicInfo', 0] },
                    // Use existing commentCount and likeCount fields
                    interactionScore: {
                        $add: [
                            { $multiply: [{ $ifNull: ['$likeCount', 0] }, 3] },
                            { $multiply: [{ $ifNull: ['$commentCount', 0] }, 2] },
                            { $divide: [{ $ifNull: ['$views', 0] }, 10] }
                        ]
                    }
                }
            },
            {
                $sort: {
                    featured: -1,
                    interactionScore: -1,
                    views: -1
                }
            },
            {
                $project: {
                    title: 1,
                    content: 1,
                    images: 1,
                    views: 1,
                    featured: 1,
                    createdAt: 1,
                    commentCount: 1,
                    likeCount: 1,
                    interactionScore: 1,
                    'authorInfo.fullName': 1,
                    'authorInfo.username': 1,
                    'authorInfo.avatarUrl': 1,
                    'topicInfo.name': 1,
                    'topicInfo.color': 1,
                    'topicInfo.category': 1
                }
            },
            { $limit: 10 }
        ]);

        console.log(`🔧 API Aggregation returned: ${featuredPostsAPI.length} posts`);

        if (featuredPostsAPI.length > 0) {
            console.log('\n📋 API Results:');
            featuredPostsAPI.forEach((post, index) => {
                console.log(`${index + 1}. "${post.title}"`);
                console.log(`   Featured: ${post.featured}`);
                console.log(`   Interaction Score: ${post.interactionScore}`);
                console.log(`   Author: ${post.authorInfo?.fullName || 'Unknown'}`);
                console.log(`   Topic: ${post.topicInfo?.name || 'Unknown'}`);
                console.log('   ' + '-'.repeat(40));
            });
        }

        // Test setting some posts as featured
        console.log('\n🔧 Setting top 3 posts as featured for testing...');

        const topPosts = await Post.find()
            .sort({ views: -1 })
            .limit(3)
            .select('title views');

        if (topPosts.length > 0) {
            const topPostIds = topPosts.map(post => post._id);

            // First, unset all featured posts
            await Post.updateMany({}, { $set: { featured: false } });
            console.log('✅ Cleared all featured flags');

            // Then set top posts as featured
            const updateResult = await Post.updateMany(
                { _id: { $in: topPostIds } },
                { $set: { featured: true } }
            );

            console.log(`✅ Set ${updateResult.modifiedCount} posts as featured`);

            topPosts.forEach((post, index) => {
                console.log(`${index + 1}. "${post.title}" (${post.views} views) -> Featured`);
            });
        }

        // Final check
        const finalFeaturedCount = await Post.countDocuments({ featured: true });
        console.log(`\n🎯 Final featured posts count: ${finalFeaturedCount}`);

    } catch (error) {
        console.error('❌ Error debugging featured posts:', error);
    }

    mongoose.connection.close();
};

debugFeaturedPosts();
