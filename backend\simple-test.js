console.log('Starting simple test...');

try {
    console.log('Node.js version:', process.version);
    console.log('Current directory:', process.cwd());
    
    // Test require statements
    const express = require('express');
    console.log('✅ Express loaded');
    
    const http = require('http');
    console.log('✅ HTTP loaded');
    
    const socketIo = require('socket.io');
    console.log('✅ Socket.IO loaded');
    
    const cors = require('cors');
    console.log('✅ CORS loaded');
    
    console.log('✅ All dependencies loaded successfully');
    
    // Test basic server
    const app = express();
    const server = http.createServer(app);
    
    app.get('/test', (req, res) => {
        res.json({ message: 'Test OK' });
    });
    
    server.listen(5001, () => {
        console.log('🚀 Simple test server running on port 5001');
    });
    
} catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
}
