// File: backend/test-chatbot-offline.js
// Test chatbot logic offline (không cần server chạy)

const mongoose = require('mongoose');
const ChatbotIntent = require('./models/ChatbotIntent');
const ChatbotConversation = require('./models/ChatbotConversation');
const User = require('./models/User'); // Import User model
const DialogflowService = require('./services/dialogflowService');
require('dotenv').config();

async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

async function testDialogflowService() {
    console.log('\n=== Test Dialogflow Service ===');

    try {
        // Test detect intent
        console.log('1. Test detect intent...');
        const dialogflowService = DialogflowService.instance();
        const sessionId = 'test-session-' + Date.now();
        const result = await dialogflowService.detectIntent(sessionId, 'xin chào');

        if (result.success) {
            console.log('✅ Detect intent thành công');
            console.log(`   Intent: ${result.data.intent.displayName}`);
            console.log(`   Response: ${result.data.fulfillmentText}`);
            console.log(`   Confidence: ${result.data.intent.confidence}`);
        } else {
            console.log('❌ Detect intent thất bại:', result.error);
        }

        // Test list intents
        console.log('\n2. Test list intents...');
        const listResult = await dialogflowService.listIntents();

        if (listResult.success) {
            console.log('✅ List intents thành công');
            console.log(`   Số lượng intents: ${listResult.data.length}`);
            listResult.data.slice(0, 3).forEach(intent => {
                console.log(`   - ${intent.displayName} (${intent.trainingPhrasesCount} phrases)`);
            });
        } else {
            console.log('❌ List intents thất bại:', listResult.error);
        }

    } catch (error) {
        console.log('❌ Lỗi test Dialogflow service:', error.message);
    }
}

async function testDatabaseOperations() {
    console.log('\n=== Test Database Operations ===');

    try {
        // Test count intents
        console.log('1. Test count intents...');
        const intentCount = await ChatbotIntent.countDocuments();
        console.log(`✅ Số lượng intents trong DB: ${intentCount}`);

        // Test get intents
        console.log('\n2. Test get intents...');
        const intents = await ChatbotIntent.find({}).limit(3);
        console.log(`✅ Lấy được ${intents.length} intents:`);
        intents.forEach(intent => {
            console.log(`   - ${intent.displayName} (${intent.trainingPhrases.length} phrases, ${intent.responses.length} responses)`);
            console.log(`     Status: ${intent.status}, Sync: ${intent.dialogflow?.syncStatus || 'N/A'}`);
        });

        // Test conversation count
        console.log('\n3. Test count conversations...');
        const conversationCount = await ChatbotConversation.countDocuments();
        console.log(`✅ Số lượng conversations trong DB: ${conversationCount}`);

        // Test analytics
        console.log('\n4. Test analytics...');
        const popularIntents = await ChatbotIntent.getPopularIntents(5);
        console.log(`✅ Top intents:`);
        popularIntents.forEach((intent, index) => {
            console.log(`   ${index + 1}. ${intent.displayName} (${intent.stats?.triggerCount || 0} triggers)`);
        });

    } catch (error) {
        console.log('❌ Lỗi test database:', error.message);
    }
}

async function testCreateIntent() {
    console.log('\n=== Test Create Intent ===');

    try {
        const testIntentData = {
            name: 'test_intent_' + Date.now(),
            displayName: 'Test Intent',
            description: 'Intent test tự động',
            category: 'other',
            trainingPhrases: [
                { text: 'test phrase 1' },
                { text: 'test phrase 2' }
            ],
            responses: [
                { type: 'text', text: 'Test response 1' },
                { type: 'text', text: 'Test response 2' }
            ],
            status: 'draft',
            createdBy: new mongoose.Types.ObjectId()
        };

        // Tạo intent trong database
        console.log('1. Tạo intent trong database...');
        const intent = new ChatbotIntent(testIntentData);
        await intent.save();
        console.log(`✅ Đã tạo intent: ${intent.displayName}`);

        // Sync với Dialogflow
        console.log('2. Sync với Dialogflow...');
        try {
            const dialogflowService = DialogflowService.instance();
            const dialogflowResult = await dialogflowService.createIntent({
                displayName: intent.displayName,
                trainingPhrases: intent.trainingPhrases,
                responses: intent.responses
            });

            if (dialogflowResult.success) {
                console.log('✅ Sync Dialogflow thành công');
                intent.dialogflow.intentId = dialogflowResult.data.intentId;
                intent.dialogflow.syncStatus = 'synced';
                intent.status = 'active';
                await intent.save();
            } else {
                console.log('❌ Sync Dialogflow thất bại:', dialogflowResult.error);
                intent.dialogflow.syncStatus = 'error';
                intent.dialogflow.syncError = dialogflowResult.error;
                await intent.save();
            }
        } catch (syncError) {
            console.log('❌ Lỗi sync:', syncError.message);
        }

        // Test intent vừa tạo
        console.log('3. Test intent vừa tạo...');
        const dialogflowService2 = DialogflowService.instance();
        const testResult = await dialogflowService2.detectIntent(
            'test-session-' + Date.now(),
            'test phrase 1'
        );

        if (testResult.success) {
            console.log('✅ Test intent thành công');
            console.log(`   Detected: ${testResult.data.intent.displayName}`);
            console.log(`   Response: ${testResult.data.fulfillmentText}`);
        } else {
            console.log('❌ Test intent thất bại:', testResult.error);
        }

        // Cleanup - xóa intent test
        console.log('4. Cleanup...');
        if (intent.dialogflow.intentId) {
            const dialogflowService3 = DialogflowService.instance();
            await dialogflowService3.deleteIntent(intent.dialogflow.intentId);
        }
        await ChatbotIntent.findByIdAndDelete(intent._id);
        console.log('✅ Đã xóa intent test');

    } catch (error) {
        console.log('❌ Lỗi test create intent:', error.message);
    }
}

async function testConversationAnalytics() {
    console.log('\n=== Test Conversation Analytics ===');

    try {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        const endDate = new Date();

        // Test conversation stats
        console.log('1. Test conversation stats...');
        const stats = await ChatbotConversation.getConversationStats(startDate, endDate);
        if (stats.length > 0) {
            console.log('✅ Conversation stats:');
            console.log(`   Total conversations: ${stats[0].totalConversations || 0}`);
            console.log(`   Avg duration: ${Math.round(stats[0].avgDuration || 0)}s`);
            console.log(`   Avg messages: ${Math.round(stats[0].avgMessages || 0)}`);
            console.log(`   Avg rating: ${(stats[0].avgRating || 0).toFixed(1)}`);
        } else {
            console.log('ℹ️  Chưa có conversation data');
        }

        // Test popular intents from conversations
        console.log('\n2. Test popular intents from conversations...');
        const popularIntents = await ChatbotConversation.getPopularIntents(startDate, endDate, 5);
        if (popularIntents.length > 0) {
            console.log('✅ Popular intents from conversations:');
            popularIntents.forEach((intent, index) => {
                console.log(`   ${index + 1}. ${intent.displayName || intent._id} (${intent.count} times)`);
            });
        } else {
            console.log('ℹ️  Chưa có conversation data');
        }

    } catch (error) {
        console.log('❌ Lỗi test analytics:', error.message);
    }
}

async function main() {
    console.log('🤖 Chatbot Offline Test Suite\n');

    await connectDB();

    const args = process.argv.slice(2);

    if (args.includes('--dialogflow-only')) {
        await testDialogflowService();
    } else if (args.includes('--database-only')) {
        await testDatabaseOperations();
    } else if (args.includes('--create-only')) {
        await testCreateIntent();
    } else if (args.includes('--analytics-only')) {
        await testConversationAnalytics();
    } else {
        // Test tất cả
        await testDatabaseOperations();
        await testDialogflowService();
        await testCreateIntent();
        await testConversationAnalytics();
    }

    console.log('\n✨ Hoàn thành offline test!');
    process.exit(0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    testDialogflowService,
    testDatabaseOperations,
    testCreateIntent,
    testConversationAnalytics
};
