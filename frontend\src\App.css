.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {

  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);

}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Đặt các quy tắc này vào App.css hoặc một file CSS chung */

.post-content img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  display: block;
  object-fit: cover;
  image-rendering: auto;
  /* Áp dụng transition cho hiệu ứng mượt mà */
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  cursor: pointer;
  /* <PERSON><PERSON> hiển thị con trỏ con trỏ */
}

/* <PERSON><PERSON><PERSON> ứng transform khi hover */
.post-content img:hover {
  transform: scale(1.015);
}