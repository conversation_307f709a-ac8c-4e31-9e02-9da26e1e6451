// Test Google OAuth Configuration
require('dotenv').config();
const { OAuth2Client } = require('google-auth-library');

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

console.log('🔍 Testing Google OAuth Configuration...\n');

console.log('📋 Environment Variables:');
console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID);
console.log('GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? '***SET***' : 'NOT SET');
console.log('GOOGLE_REDIRECT_URI:', process.env.GOOGLE_REDIRECT_URI);

console.log('\n✅ Google OAuth Client initialized successfully');

// Test function to verify a token (you would need a real token to test)
const testTokenVerification = async (testToken) => {
    try {
        console.log('\n🔐 Testing token verification...');
        
        const ticket = await client.verifyIdToken({
            idToken: testToken,
            audience: process.env.GOOGLE_CLIENT_ID
        });
        
        const payload = ticket.getPayload();
        console.log('✅ Token verified successfully');
        console.log('User info:', {
            email: payload.email,
            name: payload.name,
            picture: payload.picture
        });
        
    } catch (error) {
        console.error('❌ Token verification failed:', error.message);
    }
};

console.log('\n📝 To test with a real token:');
console.log('1. Get a Google ID token from frontend');
console.log('2. Call testTokenVerification(token)');
console.log('\n🚀 Configuration check complete!');

module.exports = { testTokenVerification };
