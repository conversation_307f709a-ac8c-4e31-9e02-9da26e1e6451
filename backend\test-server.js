const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// CORS configuration
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    credentials: true
}));

// Socket.IO configuration
const io = socketIo(server, {
    cors: {
        origin: ['http://localhost:5173', 'http://localhost:3000'],
        methods: ['GET', 'POST'],
        credentials: true
    }
});

app.use(express.json());

// Simple test route
app.get('/test', (req, res) => {
    res.json({ message: 'Backend is working!', timestamp: new Date() });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('🔌 User connected:', socket.id);

    socket.on('joinUserRoom', (userId) => {
        console.log('🏠 User joined room:', userId);
        socket.emit('test', {
            message: 'Connection successful',
            userId: userId,
            socketId: socket.id,
            timestamp: new Date().toISOString()
        });
    });

    socket.on('sendMessage', (data) => {
        console.log('📨 Message received:', data);
        
        // Echo the message back
        socket.emit('messageSent', {
            success: true,
            message: {
                _id: 'test_' + Date.now(),
                content: data.content,
                senderId: data.senderId,
                receiverId: data.receiverId,
                createdAt: new Date()
            }
        });

        // Broadcast to all clients (for testing)
        io.emit('newMessage', {
            _id: 'test_' + Date.now(),
            content: data.content,
            senderId: data.senderId,
            receiverId: data.receiverId,
            createdAt: new Date()
        });
    });

    socket.on('deleteMessage', (data) => {
        console.log('🗑️ Delete message:', data);
        io.emit('messageDeleted', { messageId: data.messageId });
    });

    socket.on('disconnect', () => {
        console.log('🔌 User disconnected:', socket.id);
    });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
    console.log(`🚀 Test server running on port ${PORT}`);
    console.log(`📡 Socket.IO server ready`);
});
