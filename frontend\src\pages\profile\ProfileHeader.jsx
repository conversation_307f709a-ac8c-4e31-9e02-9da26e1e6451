// filepath: d:\HỌC CNTT\HK2 2024-2025\KHÓA LUẬN TỐT NGHIỆP\DU AN\hilu-auau\frontend\src\pages\profile\ProfileHeader.jsx
import React, { useState, useEffect, useRef, useContext } from 'react';
import { Box, Typography, Avatar, Button, useTheme, Dialog, DialogTitle, DialogContent, DialogActions, TextField, CircularProgress, Switch, FormControlLabel, Fade } from '@mui/material';
import CakeIcon from '@mui/icons-material/Cake';
import PostAddIcon from '@mui/icons-material/PostAdd';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import EditIcon from '@mui/icons-material/Edit';
import ChatIcon from '@mui/icons-material/Chat'; // Added ChatIcon
import axios from 'axios';
import { useNavigate } from "react-router-dom"; // Added useNavigate
import { AuthContext } from "../../context/AuthContext"; // Added AuthContext

const ProfileHeader = ({ userData, isCurrentUser, onProfileUpdate }) => {
    const theme = useTheme();
    const navigate = useNavigate(); // Initialize useNavigate
    const { user: currentUser } = useContext(AuthContext); // Get current user from context
    const [openEdit, setOpenEdit] = useState(false);
    const [form, setForm] = useState({
        fullName: userData.fullName || '',
        email: userData.email || '',
        phoneNumber: userData.phoneNumber || '',
        isPhoneNumberHidden: userData.isPhoneNumberHidden || false,
        bio: userData.bio || '',
        avatarUrl: userData.avatarUrl || '',
        coverPhotoUrl: userData.coverPhotoUrl || '' // Add coverPhotoUrl to form state
    });
    const [avatarFile, setAvatarFile] = useState(null);
    const [coverPhotoFile, setCoverPhotoFile] = useState(null); // New state for cover photo file
    const [loading, setLoading] = useState(false);
    const avatarInputRef = useRef(null); // Renamed fileInputRef to avatarInputRef
    const coverPhotoInputRef = useRef(null); // New ref for cover photo input
    const [stats, setStats] = useState({
        postCount: 0,
        commentCount: 0,
        likeCount: 0
    });

    const joinedDate = new Date(userData.createdAt).toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });
    useEffect(() => {
        const fetchCounts = async () => {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    console.error('No token found');
                    return;
                }

                const config = {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                };

                const response = await axios.get(
                    `http://localhost:5000/api/users/stats/${userData._id}`,
                    config
                );
                setStats(response.data);

            } catch (error) {
                console.error("Error fetching counts:", error);
            }
        };

        if (userData && userData._id) {
            fetchCounts();
        }
    }, [userData]);

    const handleOpenEdit = () => setOpenEdit(true);
    const handleCloseEdit = () => setOpenEdit(false);

    const handleChange = (e) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const handleSave = async () => {
        setLoading(true);
        const formData = new FormData();
        formData.append('fullName', form.fullName);
        formData.append('email', form.email);
        formData.append('phoneNumber', form.phoneNumber);
        formData.append('isPhoneNumberHidden', form.isPhoneNumberHidden);
        formData.append('bio', form.bio);

        if (avatarFile) {
            formData.append('avatar', avatarFile);
        } else {
            formData.append('avatarUrl', form.avatarUrl);
        }

        if (coverPhotoFile) { // Append cover photo file if exists
            formData.append('coverPhoto', coverPhotoFile);
        } else {
            formData.append('coverPhotoUrl', form.coverPhotoUrl); // Append existing cover photo URL
        }

        try {
            const response = await axios.put('http://localhost:5000/api/users/me', formData, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'multipart/form-data'
                }
            });
            if (onProfileUpdate) onProfileUpdate(response.data.user);
            setOpenEdit(false);
            setAvatarFile(null);
        } catch (err) {
            console.error("Update error:", err.response ? err.response.data : err);
            alert('Cập nhật thất bại!');
        }
        setLoading(false);
    };

    const handleAvatarChange = (e) => { // Renamed handleFileChange to handleAvatarChange
        const file = e.target.files[0];
        if (file) {
            setAvatarFile(file);
            // Preview the image
            const reader = new FileReader();
            reader.onloadend = () => {
                setForm(prevForm => ({ ...prevForm, avatarUrl: reader.result }));
            };
            reader.readAsDataURL(file);
        }
    };

    const handleCoverPhotoChange = (e) => { // New handler for cover photo
        const file = e.target.files[0];
        if (file) {
            setCoverPhotoFile(file);
            // Preview the image
            const reader = new FileReader();
            reader.onloadend = () => {
                setForm(prevForm => ({ ...prevForm, coverPhotoUrl: reader.result }));
            };
            reader.readAsDataURL(file);
        }
    };

    const handleAvatarClick = () => {
        avatarInputRef.current.click(); // Updated ref name
    };

    const handleCoverPhotoClick = () => { // New handler for cover photo click
        coverPhotoInputRef.current.click();
    };

    return (
        <Box
            sx={{
                position: 'relative',
                width: '100%',
                borderRadius: 2,
                overflow: 'hidden',
                mb: 4,
                backgroundColor: theme.palette.background.paper,
                boxShadow: theme.palette.mode === 'dark' ? 'none' : '0px 2px 8px rgba(0,0,0,0.05)',
                transition: 'background-color 0.4s ease, box-shadow 0.4s ease',
            }}
        >
            {/* Ảnh bìa với gradient overlay */}
            <Box
                sx={{
                    height: { xs: 180, sm: 220, md: 280 },
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Always show gradient
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'flex-end',
                    justifyContent: 'center',
                    overflow: 'hidden', // Ensure image doesn't overflow
                    '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: 'linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%)',
                        pointerEvents: 'none'
                    }
                }}
            >
                {form.coverPhotoUrl && (
                    <Fade in={true} timeout={1000}> {/* Fade in the image */}
                        <Box
                            component="img"
                            src={form.coverPhotoUrl}
                            alt="Cover Photo"
                            sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                                zIndex: 0, // Behind other content
                            }}
                        />
                    </Fade>
                )}
                {/* Decorative elements */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: 20,
                        right: 20,
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        background: 'rgba(255,255,255,0.1)',
                        backdropFilter: 'blur(10px)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: '1px solid rgba(255,255,255,0.2)',
                        zIndex: 1, // Ensure decorative elements are above the image
                    }}
                >
                    <Box
                        sx={{
                            width: 30,
                            height: 30,
                            borderRadius: '50%',
                            background: 'rgba(255,255,255,0.3)',
                        }}
                    />
                </Box>
            </Box>

            {/* Avatar và thông tin */}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    alignItems: { xs: 'center', md: 'flex-end' },
                    p: { xs: 2, md: 3 },
                    mt: { xs: -8, md: -10 },
                    position: 'relative',
                    zIndex: 1,
                }}
            >
                {/* Avatar với hiệu ứng đẹp */}
                <Box sx={{ position: 'relative' }}>
                    <Avatar
                        alt={userData.fullName}
                        src={userData.avatarUrl || '/admin-avatar.png'}
                        sx={{
                            width: { xs: 140, sm: 160, md: 180 },
                            height: { xs: 140, sm: 160, md: 180 },
                            border: `6px solid ${theme.palette.background.paper}`,
                            boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
                            zIndex: 2,
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                            '&:hover': {
                                transform: 'scale(1.05)',
                                boxShadow: '0 12px 40px rgba(0,0,0,0.25)',
                            },
                        }}
                    />
                    {/* Online status indicator */}
                    {userData.isOnline && (
                        <Box
                            sx={{
                                position: 'absolute',
                                bottom: 12,
                                right: 12,
                                width: 28,
                                height: 28,
                                borderRadius: '50%',
                                backgroundColor: '#4CAF50',
                                border: `4px solid ${theme.palette.background.paper}`,
                                boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                                zIndex: 3,
                                '&::before': {
                                    content: '""',
                                    position: 'absolute',
                                    top: '50%',
                                    left: '50%',
                                    transform: 'translate(-50%, -50%)',
                                    width: '60%',
                                    height: '60%',
                                    borderRadius: '50%',
                                    backgroundColor: 'rgba(255,255,255,0.3)',
                                }
                            }}
                        />
                    )}
                </Box>
                <Box
                    sx={{
                        ml: { xs: 0, md: 3 },
                        mt: { xs: 2, md: 0 },
                        textAlign: { xs: 'center', md: 'left' },
                        flexGrow: 1,
                    }}
                >
                    <Typography
                        variant="h4"
                        component="h1"
                        sx={{
                            fontWeight: 'bold',
                            color: theme.palette.text.primary,
                            transition: 'color 0.4s ease',
                        }}
                    >
                        {userData.fullName || userData.username}
                    </Typography>
                    <Typography
                        variant="h6"
                        sx={{
                            color: theme.palette.text.secondary,
                            mb: 1,
                            transition: 'color 0.4s ease',
                        }}
                    >
                        @{userData.username}
                    </Typography>
                    <Typography
                        variant="body1"
                        sx={{
                            color: theme.palette.text.secondary,
                            transition: 'color 0.4s ease',
                        }}
                    >
                        {userData.bio || "Chưa có mô tả"}
                    </Typography>

                    {/* Thống kê nhanh */}
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: { xs: 'center', md: 'flex-start' },
                            gap: { xs: 2, sm: 4 },
                            mt: 2,
                            flexWrap: 'wrap',
                        }}
                    >
                        <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.text.secondary }}>
                            <PostAddIcon fontSize="small" sx={{ mr: 0.5 }} />
                            <Typography variant="body2">{stats.postCount} Bài viết</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.text.secondary }}>
                            <ChatBubbleOutlineIcon fontSize="small" sx={{ mr: 0.5 }} />
                            <Typography variant="body2">{stats.commentCount} Bình luận</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.text.secondary }}>
                            <FavoriteBorderIcon fontSize="small" sx={{ mr: 0.5 }} />
                            <Typography variant="body2">{stats.likeCount} Lượt thích</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.text.secondary }}>
                            <CakeIcon fontSize="small" sx={{ mr: 0.5 }} />
                            <Typography variant="body2">Tham gia: {joinedDate}</Typography>
                        </Box>
                    </Box>
                </Box>

                {/* Nút chỉnh sửa (chỉ hiện nếu là trang của mình) */}
                <Box
                    sx={{
                        mt: { xs: 2, md: 0 },
                        ml: { xs: 0, md: 'auto' },
                        display: 'flex',
                        gap: 2, // Add gap between buttons
                    }}
                >
                    {!isCurrentUser && (
                        <Button
                            variant="contained"
                            color="primary"
                            startIcon={<ChatIcon />}
                            onClick={() => navigate(`/chat`, { state: { recipientId: userData._id } })}
                            sx={{
                                borderRadius: 5,
                                px: 3,
                                py: 1,
                                fontSize: '1rem',
                                fontWeight: 600,
                                textTransform: 'none',
                                boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                                '&:hover': {
                                    background: 'linear-gradient(45deg, #1976D2 30%, #19B3DA 90%)',
                                    boxShadow: '0 6px 15px rgba(0,0,0,0.2)',
                                }
                            }}
                        >
                            Chat
                        </Button>
                    )}
                    {isCurrentUser && (
                        <Button
                            variant="contained"
                            color="primary"
                            startIcon={<EditIcon />}
                            sx={{
                                px: 3,
                                py: 1,
                                borderRadius: 5,
                                minWidth: 150,
                                boxShadow: theme.palette.mode === 'dark' ? 'none' : '0px 2px 5px rgba(0,0,0,0.2)',
                                transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
                            }}
                            onClick={handleOpenEdit}
                        >
                            Chỉnh sửa
                        </Button>
                    )}
                </Box>
            </Box>

            {/* Dialog chỉnh sửa */}
            <Dialog open={openEdit} onClose={handleCloseEdit} maxWidth="xs" fullWidth>
                <DialogTitle>Chỉnh sửa hồ sơ</DialogTitle>
                <DialogContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                        <Avatar
                            src={form.avatarUrl}
                            sx={{ width: 120, height: 120, mb: 2, cursor: 'pointer' }}
                            onClick={handleAvatarClick}
                        />
                        <Button variant="outlined" onClick={handleAvatarClick}>
                            Tải ảnh lên
                        </Button>
                        <input
                            type="file"
                            ref={avatarInputRef} // Updated ref name
                            onChange={handleAvatarChange} // Updated handler name
                            accept="image/*"
                            hidden
                        />
                    </Box>

                    {/* Cover Photo Upload Section */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2, mt: 2 }}>
                        <Typography variant="subtitle1" sx={{ mb: 1 }}>Ảnh bìa</Typography>
                        <Box
                            sx={{
                                width: '100%',
                                height: 120,
                                borderRadius: 2,
                                overflow: 'hidden',
                                mb: 2,
                                background: form.coverPhotoUrl
                                    ? `url(${form.coverPhotoUrl}) center / cover no-repeat`
                                    : 'linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'text.secondary',
                                cursor: 'pointer',
                                border: '1px dashed',
                                borderColor: 'divider',
                            }}
                            onClick={handleCoverPhotoClick}
                        >
                            {!form.coverPhotoUrl && <Typography variant="body2">Chọn ảnh bìa</Typography>}
                        </Box>
                        <Button variant="outlined" onClick={handleCoverPhotoClick}>
                            Tải ảnh bìa lên
                        </Button>
                        <input
                            type="file"
                            ref={coverPhotoInputRef}
                            onChange={handleCoverPhotoChange}
                            accept="image/*"
                            hidden
                        />
                    </Box>

                    <TextField
                        margin="normal"
                        label="Họ tên"
                        name="fullName"
                        value={form.fullName}
                        onChange={handleChange}
                        fullWidth
                    />
                    <TextField
                        margin="normal"
                        label="Email"
                        name="email"
                        type="email"
                        value={form.email}
                        onChange={handleChange}
                        fullWidth
                    />
                    <TextField
                        margin="normal"
                        label="Số điện thoại"
                        name="phoneNumber"
                        value={form.phoneNumber}
                        onChange={handleChange}
                        fullWidth
                    />
                    <FormControlLabel
                        control={
                            <Switch
                                checked={form.isPhoneNumberHidden}
                                onChange={(e) => setForm({ ...form, isPhoneNumberHidden: e.target.checked })}
                                name="isPhoneNumberHidden"
                            />
                        }
                        label="Ẩn số điện thoại"
                    />
                    <TextField
                        margin="normal"
                        label="Mô tả bản thân"
                        name="bio"
                        value={form.bio}
                        onChange={handleChange}
                        fullWidth
                        multiline
                        rows={3}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseEdit}>Hủy</Button>
                    <Button onClick={handleSave} variant="contained" disabled={loading}>
                        {loading ? <CircularProgress size={20} /> : "Lưu"}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default ProfileHeader;
