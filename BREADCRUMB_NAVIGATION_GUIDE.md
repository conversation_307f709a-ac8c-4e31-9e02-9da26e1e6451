# 🧭 Global Breadcrumb Navigation System

## 🎯 **Hệ thống điều hướng toàn cục cho tất cả trang**

Đã tạo **BreadcrumbNavigation component** để cung cấp thanh điều hướng nhất quán cho toàn bộ website.

### 📊 **Breadcrumb Structure cho các trang**

```
🏠 Trang chủ
├── 📚 Khoa học máy tính (Topic Detail)
│   └── 📄 Bài viết ABC (Post Detail)
├── 👤 Hồ sơ - Nguyễn Văn A (Profile)
├── 🔍 Tìm kiếm: "react" (Search)
├── ℹ️ Giới thiệu (About)
└── 📧 Liên hệ (Contact)
```

## 🔧 **Component Implementation**

### **BreadcrumbNavigation.jsx**
```jsx
import React from 'react';
import { Breadcrumbs, Typography, Box, useTheme } from '@mui/material';
import { Link, useLocation } from 'react-router-dom';
import HomeIcon from '@mui/icons-material/Home';
import TopicIcon from '@mui/icons-material/Topic';
import ArticleIcon from '@mui/icons-material/Article';
// ... other icons

const BreadcrumbNavigation = ({ 
    customBreadcrumbs = null,
    topicName = null,
    postTitle = null,
    userName = null,
    searchQuery = null,
    darkMode = false 
}) => {
    const theme = useTheme();
    const location = useLocation();

    // Auto-generate breadcrumbs dựa trên route
    const generateBreadcrumbs = () => {
        const breadcrumbs = [];
        
        // Luôn có Trang chủ
        breadcrumbs.push(
            <Link to="/" style={{ /* styles */ }}>
                <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />
                Trang chủ
            </Link>
        );

        // Logic cho từng trang...
        return breadcrumbs;
    };

    return (
        <Box sx={{ 
            py: 2, 
            px: 3,
            backgroundColor: darkMode ? '#1a1b1c' : '#f8f9fa',
            borderBottom: `1px solid ${darkMode ? '#3a3b3c' : '#e0e0e0'}`,
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
            <Breadcrumbs>
                {generateBreadcrumbs()}
            </Breadcrumbs>
        </Box>
    );
};
```

## 🎨 **Usage Examples**

### **1. Home Page**
```jsx
// ✅ Chỉ hiển thị "Trang chủ"
<BreadcrumbNavigation
    darkMode={isDarkMode}
/>

// Kết quả: 🏠 Trang chủ
```

### **2. Topic Detail Page**
```jsx
// ✅ Trang chủ > Chủ đề
<BreadcrumbNavigation
    topicName={topic?.name}
    darkMode={theme.palette.mode === 'dark'}
/>

// Kết quả: 🏠 Trang chủ > 📚 Khoa học máy tính
```

### **3. Post Detail Page**
```jsx
// ✅ Trang chủ > Chủ đề > Bài viết
<BreadcrumbNavigation
    topicName={postDetail?.topicId?.name}
    postTitle={postDetail?.title}
    darkMode={darkMode}
/>

// Kết quả: 🏠 Trang chủ > 📚 Khoa học máy tính > 📄 Bài viết ABC
```

### **4. Profile Page**
```jsx
// ✅ Trang chủ > Hồ sơ
<BreadcrumbNavigation
    userName={user?.fullName}
    darkMode={darkMode}
/>

// Kết quả: 🏠 Trang chủ > 👤 Hồ sơ - Nguyễn Văn A
```

### **5. Search Page**
```jsx
// ✅ Trang chủ > Tìm kiếm
<BreadcrumbNavigation
    searchQuery={searchTerm}
    darkMode={darkMode}
/>

// Kết quả: 🏠 Trang chủ > 🔍 Tìm kiếm: "react"
```

### **6. Custom Breadcrumbs**
```jsx
// ✅ Custom breadcrumbs cho trang đặc biệt
const customBreadcrumbs = [
    <Link to="/" key="home">🏠 Trang chủ</Link>,
    <Link to="/admin" key="admin">⚙️ Quản trị</Link>,
    <Typography key="users">👥 Quản lý người dùng</Typography>
];

<BreadcrumbNavigation
    customBreadcrumbs={customBreadcrumbs}
    darkMode={darkMode}
/>
```

## 🎯 **Auto-Detection Logic**

### **Route-based Detection**
```javascript
// ✅ Tự động phát hiện trang dựa trên pathname
const pathname = location.pathname;

if (pathname.includes('/topic/') && topicName) {
    // Topic Detail page
} else if (pathname.includes('/post-detail') && topicName && postTitle) {
    // Post Detail page  
} else if (pathname.includes('/profile') && userName) {
    // Profile page
} else if (pathname.includes('/search') && searchQuery) {
    // Search page
} else if (pathname === '/about') {
    // About page
} else if (pathname === '/contact') {
    // Contact page
}
```

### **URL Parameters**
```javascript
// ✅ Lấy topicId từ URL params cho Post Detail
const topicId = new URLSearchParams(location.search).get('topicId');

// Tạo link về Topic Detail
<Link to={`/topic/${topicId}`}>
    <TopicIcon sx={{ mr: 0.5, fontSize: 16 }} />
    {topicName}
</Link>
```

## 🎨 **Styling & Theming**

### **Dark Mode Support**
```jsx
// ✅ Tự động thay đổi theo theme
<Box sx={{ 
    backgroundColor: darkMode ? '#1a1b1c' : '#f8f9fa',
    borderBottom: `1px solid ${darkMode ? '#3a3b3c' : '#e0e0e0'}`,
    boxShadow: darkMode 
        ? '0 1px 3px rgba(0,0,0,0.3)' 
        : '0 1px 3px rgba(0,0,0,0.1)'
}}>
```

### **Link Styling**
```jsx
// ✅ Consistent link styling
<Link style={{
    textDecoration: 'none',
    color: darkMode ? '#90caf9' : theme.palette.primary.main,
    display: 'flex',
    alignItems: 'center',
    fontSize: '0.875rem',
    fontWeight: 500
}}>
```

### **Typography Styling**
```jsx
// ✅ Current page styling
<Typography sx={{
    display: 'flex',
    alignItems: 'center',
    color: darkMode ? '#e4e6eb' : 'text.primary',
    fontSize: '0.875rem',
    fontWeight: 500,
    maxWidth: '300px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
}}>
```

## 📱 **Responsive Design**

### **Mobile Optimization**
```jsx
// ✅ Responsive breadcrumbs
<Breadcrumbs sx={{
    '& .MuiBreadcrumbs-separator': {
        color: darkMode ? '#b0b3b8' : 'text.secondary',
        fontSize: '0.875rem'
    },
    '& .MuiBreadcrumbs-ol': {
        flexWrap: 'wrap'  // Wrap trên mobile
    }
}}>
```

### **Text Truncation**
```jsx
// ✅ Truncate long post titles
<Typography sx={{
    maxWidth: '300px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
}}>
    {postTitle}
</Typography>
```

## 🔍 **Navigation Features**

### **Clickable Navigation**
- **🏠 Trang chủ**: Luôn clickable, về trang chủ
- **📚 Topic Name**: Clickable trong Post Detail, về Topic Detail
- **📄 Post Title**: Current page, không clickable
- **👤 Profile**: Current page, không clickable

### **URL Generation**
```javascript
// ✅ Dynamic URL generation
// Topic Detail: /topic/{topicId}
// Post Detail: /post-detail?topicId={topicId}&postId={postId}
// Profile: /profile/{userId}
// Search: /search?q={searchQuery}
```

## 🎯 **Benefits**

### **✅ Consistent Navigation**
- **Unified design**: Cùng style trên tất cả trang
- **Predictable behavior**: User biết cách navigate
- **Professional appearance**: Clean, modern breadcrumbs

### **✅ Better UX**
- **Clear hierarchy**: Hiển thị vị trí hiện tại
- **Easy navigation**: Click để quay lại trang trước
- **Context awareness**: Biết đang ở đâu trong website

### **✅ SEO Benefits**
- **Structured navigation**: Search engines hiểu structure
- **Internal linking**: Better link juice distribution
- **User engagement**: Easier navigation = longer sessions

### **✅ Accessibility**
- **Screen reader friendly**: Proper ARIA labels
- **Keyboard navigation**: Tab-friendly
- **High contrast**: Good visibility in both themes

## 📋 **Implementation Checklist**

### **✅ Completed Pages**
- [x] **Home Page**: Basic breadcrumb (chỉ Trang chủ)
- [x] **Topic Detail**: Trang chủ > Topic Name
- [x] **Post Detail**: Trang chủ > Topic Name > Post Title
- [ ] **Profile Page**: Trang chủ > Hồ sơ - User Name
- [ ] **Search Page**: Trang chủ > Tìm kiếm: "query"
- [ ] **About Page**: Trang chủ > Giới thiệu
- [ ] **Contact Page**: Trang chủ > Liên hệ

### **🔄 Next Steps**
1. **Add to remaining pages**: Profile, Search, About, Contact
2. **Test navigation**: Verify all links work correctly
3. **Mobile testing**: Ensure responsive behavior
4. **Accessibility audit**: Screen reader compatibility
5. **Performance check**: No impact on page load

---

**🧭 Global Breadcrumb Navigation hoàn hảo!**

**Consistent**: Cùng design trên tất cả trang
**Intuitive**: Easy navigation và clear hierarchy  
**Professional**: Clean, modern appearance
**Accessible**: Screen reader friendly và keyboard navigation
