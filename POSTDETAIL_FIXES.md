# 🔧 PostDetail - Sửa lỗi và Hướng dẫn

## ✅ Đã sửa các lỗi

### 1. **ReferenceError: Cannot access 'calculateReadTime' before initialization**
**Vấn đề**: Hàm `calculateReadTime` được sử dụng trong `useEffect` trước khi được định nghĩa.

**Giải pháp**: 
- <PERSON> chuyển định nghĩa hàm `calculateReadTime` và `handleScroll` lên trước `useEffect`
- Sử dụng `useCallback` để tối ưu performance
- Xóa các định nghĩa hàm trùng lặp

```jsx
// ✅ Đúng - Định nghĩa hàm trước
const calculateReadTime = useCallback((content) => {
    const wordsPerMinute = 200;
    const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
    return Math.ceil(words / wordsPerMinute);
}, []);

// ✅ Sau đó mới sử dụng trong useEffect
useEffect(() => {
    if (postDetail?.content) {
        const readTime = calculateReadTime(postDetail.content);
        setEstimatedReadTime(readTime);
    }
}, [postDetail, calculateReadTime]);
```

### 2. **Undefined function handleLikePost**
**Vấn đề**: Sử dụng `handleLikePost` nhưng từ hook chỉ có `handleLikeToggle`.

**Giải pháp**:
- Thay `handleLikePost(postDetail._id)` → `handleLikeToggle`
- Thay `postDetail.isLikedByUser` → `isLikedByUser`
- Thay `postDetail.likeCount` → `currentLikeCount`

```jsx
// ✅ Đúng
<IconButton
    onClick={handleLikeToggle}
    sx={{
        color: isLikedByUser ? '#e91e63' : 'inherit',
        '&:hover': { color: '#e91e63' }
    }}
>
    {isLikedByUser ? <FavoriteIcon /> : <FavoriteBorderIcon />}
</IconButton>
<Typography variant="body2" fontWeight="bold">
    {currentLikeCount || 0}
</Typography>
```

### 3. **Import path issues**
**Vấn đề**: Import path không đúng cho một số components.

**Giải pháp**:
- Đảm bảo tất cả import paths đều chính xác
- PostForm import từ `./CenterColumn/PostForm`
- PostDetailSkeleton import từ `../../components/PostDetailSkeleton`

## 🚀 Cách test PostDetail

### 1. **URL Testing**
```
http://localhost:5173/post-detail?topicId=123&postId=456
```

### 2. **Component Testing**
Sử dụng file test đã tạo:
```jsx
import PostDetailTest from './pages/PostDetailTest';

// Render component để test
<PostDetailTest />
```

### 3. **Features Testing**

#### **Reading Progress**
- Scroll trang để xem progress bar
- Progress bar sẽ cập nhật từ 0-100%

#### **Bookmark & Share**
- Click bookmark icon để toggle bookmark
- Click share icon để mở share menu
- Click "Sao chép liên kết" để copy URL

#### **Navigation**
- Click breadcrumbs để navigate
- Click "Bài trước/Bài tiếp theo" để navigate
- Click related posts để navigate

#### **Interactions**
- Click like button để toggle like
- Click rating button để mở rating dialog
- Click comment button để mở comment dialog

#### **Responsive**
- Test trên desktop (2-column layout)
- Test trên tablet (2-column optimized)
- Test trên mobile (single column)

## 📱 Responsive Breakpoints

```jsx
// Desktop (lg+): >= 1200px
<Grid item xs={12} lg={8}>  // Main content
<Grid item xs={12} lg={4}>  // Sidebar

// Tablet (md): 900px - 1199px
// Mobile (sm-): < 900px
// Tự động stack thành single column
```

## 🎨 Theme Testing

### **Light Mode**
- Background: `#f0f2f5`
- Cards: `#fff`
- Text: `#1c1e21`

### **Dark Mode**
- Background: `#18191a`
- Cards: `#242526`
- Text: `#e4e6eb`
- Borders: `#3a3b3c`

## 🔧 Debugging Tips

### **Console Errors**
```jsx
// Check for missing props
console.log('postDetail:', postDetail);
console.log('user:', user);
console.log('theme mode:', mode);
```

### **Network Issues**
```jsx
// Check API calls in usePostDetail hook
// Verify topicId and postId from URL params
const topicId = searchParams.get('topicId');
const postId = searchParams.get('postId');
```

### **State Issues**
```jsx
// Check state values
console.log('isLikedByUser:', isLikedByUser);
console.log('currentLikeCount:', currentLikeCount);
console.log('readingProgress:', readingProgress);
```

## 📊 Performance Optimization

### **Lazy Loading**
- Images tự động lazy load
- Skeleton loading cho better UX
- Progressive enhancement

### **Memory Management**
- useCallback cho event handlers
- Cleanup event listeners trong useEffect
- Optimized re-renders

### **Bundle Size**
- Code splitting ready
- Tree shaking compatible
- Minimal dependencies

## 🎯 Best Practices

### **Error Handling**
```jsx
// Always check for data existence
{postDetail?.content && (
    <div dangerouslySetInnerHTML={{ __html: postDetail.content }} />
)}

// Provide fallbacks
{postDetail.authorId?.fullName || 'Ẩn danh'}
```

### **Accessibility**
- Proper ARIA labels
- Keyboard navigation support
- Screen reader friendly
- Color contrast compliance

### **SEO**
- Semantic HTML structure
- Meta tags ready
- Structured data ready
- Social sharing ready

## 🔮 Future Improvements

### **Performance**
- Virtual scrolling cho long content
- Image optimization
- CDN integration
- Caching strategies

### **Features**
- Reading position save
- Offline reading
- Print optimization
- Social login integration

### **Analytics**
- Reading time tracking
- Scroll depth tracking
- Engagement metrics
- A/B testing ready

---

**✅ PostDetail đã được sửa lỗi và sẵn sàng sử dụng!**
