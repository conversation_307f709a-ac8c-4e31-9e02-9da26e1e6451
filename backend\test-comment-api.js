const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5000/api';
const TEST_USER = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'testpassword123'
};

let authToken = '';
let testPostId = '';
let testCommentId = '';
let testReplyId = '';

async function testCommentAPI() {
    try {
        console.log('🧪 Testing Comment API Endpoints...\n');

        // 1. Login to get auth token
        console.log('🔐 Step 1: Authenticating user...');
        try {
            const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
                email: TEST_USER.email,
                password: TEST_USER.password
            });
            authToken = loginResponse.data.token;
            console.log('✅ Authentication successful');
        } catch (error) {
            console.log('⚠️ Login failed, user might not exist. Continuing with existing token...');
            // You might need to manually set a valid token here for testing
            // authToken = 'your_valid_token_here';
        }

        // 2. Get a test post (assuming there's at least one post)
        console.log('\n📄 Step 2: Finding a test post...');
        try {
            const postsResponse = await axios.get(`${BASE_URL}/posts`);
            if (postsResponse.data && postsResponse.data.length > 0) {
                testPostId = postsResponse.data[0]._id;
                console.log(`✅ Found test post: ${testPostId}`);
            } else {
                console.log('❌ No posts found. Please create a post first.');
                return;
            }
        } catch (error) {
            console.log('❌ Failed to get posts:', error.message);
            return;
        }

        // 3. Create a root comment
        console.log('\n💬 Step 3: Creating root comment...');
        try {
            const commentResponse = await axios.post(`${BASE_URL}/comments`, {
                postId: testPostId,
                content: 'Test API Comment - Root Level'
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            testCommentId = commentResponse.data.comment._id;
            console.log(`✅ Root comment created: ${testCommentId}`);
        } catch (error) {
            console.log('❌ Failed to create root comment:', error.response?.data?.message || error.message);
            return;
        }

        // 4. Create a reply
        console.log('\n↩️ Step 4: Creating reply...');
        try {
            const replyResponse = await axios.post(`${BASE_URL}/comments`, {
                postId: testPostId,
                content: 'Test API Reply - Level 1',
                parentCommentId: testCommentId
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            testReplyId = replyResponse.data.comment._id;
            console.log(`✅ Reply created: ${testReplyId}`);
        } catch (error) {
            console.log('❌ Failed to create reply:', error.response?.data?.message || error.message);
        }

        // 5. Create nested reply
        console.log('\n🔄 Step 5: Creating nested reply...');
        try {
            const nestedReplyResponse = await axios.post(`${BASE_URL}/comments`, {
                postId: testPostId,
                content: 'Test API Nested Reply - Level 2',
                parentCommentId: testReplyId
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            console.log(`✅ Nested reply created: ${nestedReplyResponse.data.comment._id}`);
        } catch (error) {
            console.log('❌ Failed to create nested reply:', error.response?.data?.message || error.message);
        }

        // 6. Get comments for post
        console.log('\n📋 Step 6: Retrieving comments for post...');
        try {
            const commentsResponse = await axios.get(`${BASE_URL}/comments/post/${testPostId}`);
            const comments = commentsResponse.data;
            console.log(`✅ Retrieved ${comments.length} root comments`);
            
            // Display comment structure
            function displayComments(comments, indent = '') {
                comments.forEach(comment => {
                    console.log(`${indent}├─ [Level ${comment.level || 0}] ${comment.content} (Likes: ${comment.likeCount || 0})`);
                    if (comment.replies && comment.replies.length > 0) {
                        displayComments(comment.replies, indent + '  ');
                    }
                });
            }
            
            displayComments(comments);
        } catch (error) {
            console.log('❌ Failed to get comments:', error.response?.data?.message || error.message);
        }

        // 7. Like a comment
        console.log('\n❤️ Step 7: Testing comment like...');
        try {
            const likeResponse = await axios.post(`${BASE_URL}/comments/${testCommentId}/like`, {}, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            console.log(`✅ Comment liked: ${likeResponse.data.message}`);
        } catch (error) {
            console.log('❌ Failed to like comment:', error.response?.data?.message || error.message);
        }

        // 8. Unlike the comment
        console.log('\n💔 Step 8: Testing comment unlike...');
        try {
            const unlikeResponse = await axios.post(`${BASE_URL}/comments/${testCommentId}/like`, {}, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            console.log(`✅ Comment unliked: ${unlikeResponse.data.message}`);
        } catch (error) {
            console.log('❌ Failed to unlike comment:', error.response?.data?.message || error.message);
        }

        // 9. Update a comment
        console.log('\n✏️ Step 9: Testing comment update...');
        try {
            const updateResponse = await axios.put(`${BASE_URL}/comments/${testCommentId}`, {
                content: 'Updated Test API Comment - Root Level'
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            console.log(`✅ Comment updated: ${updateResponse.data.message}`);
        } catch (error) {
            console.log('❌ Failed to update comment:', error.response?.data?.message || error.message);
        }

        // 10. Delete a comment (this will also delete nested replies)
        console.log('\n🗑️ Step 10: Testing comment deletion...');
        try {
            const deleteResponse = await axios.delete(`${BASE_URL}/comments/${testCommentId}`, {
                headers: { Authorization: `Bearer ${authToken}` },
                data: { postId: testPostId }
            });
            console.log(`✅ Comment deleted: ${deleteResponse.data.message}`);
        } catch (error) {
            console.log('❌ Failed to delete comment:', error.response?.data?.message || error.message);
        }

        console.log('\n🎉 Comment API tests completed!');

    } catch (error) {
        console.error('❌ Test suite failed:', error.message);
    }
}

// Run the tests
testCommentAPI();
