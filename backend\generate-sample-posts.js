// File: backend/generate-sample-posts.js
// Script để tạo dữ liệu mẫu posts cho topics

const mongoose = require('mongoose');
const Post = require('./models/Post');
const Topic = require('./models/Topic');
const User = require('./models/User');
require('dotenv').config();

async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

async function generateSamplePosts() {
    try {
        console.log('🔄 Tạo sample posts...');
        
        // Lấy topics và users
        const topics = await Topic.find({});
        const users = await User.find({});
        
        if (topics.length === 0) {
            console.log('❌ Không có topics trong database');
            return;
        }
        
        if (users.length === 0) {
            console.log('❌ Không có users trong database');
            return;
        }
        
        console.log(`📊 Tìm thấy ${topics.length} topics và ${users.length} users`);
        
        // Sample posts data
        const samplePostsData = [
            // Posts cho "Khoa học máy tính"
            {
                title: "Hướng dẫn học lập trình Python cho người mới bắt đầu",
                content: `
                <h2>Tại sao nên học Python?</h2>
                <p>Python là một ngôn ngữ lập trình dễ học, có cú pháp đơn giản và được sử dụng rộng rãi trong nhiều lĩnh vực như:</p>
                <ul>
                    <li>Phát triển web</li>
                    <li>Khoa học dữ liệu</li>
                    <li>Trí tuệ nhân tạo</li>
                    <li>Tự động hóa</li>
                </ul>
                
                <h3>Bước 1: Cài đặt Python</h3>
                <p>Truy cập <a href="https://python.org">python.org</a> và tải phiên bản mới nhất.</p>
                
                <h3>Bước 2: Viết chương trình đầu tiên</h3>
                <pre><code>print("Hello, World!")</code></pre>
                
                <p>Chúc các bạn học tập hiệu quả!</p>
                `,
                tags: ["python", "lập trình", "tutorial", "beginner"],
                topicName: "Khoa học máy tính"
            },
            {
                title: "So sánh các framework JavaScript phổ biến 2024",
                content: `
                <h2>Top 3 Framework JavaScript năm 2024</h2>
                
                <h3>1. React</h3>
                <p>Được phát triển bởi Facebook, React là thư viện phổ biến nhất hiện tại.</p>
                <ul>
                    <li>✅ Cộng đồng lớn</li>
                    <li>✅ Ecosystem phong phú</li>
                    <li>❌ Learning curve khá cao</li>
                </ul>
                
                <h3>2. Vue.js</h3>
                <p>Framework progressive, dễ học và linh hoạt.</p>
                <ul>
                    <li>✅ Dễ học</li>
                    <li>✅ Documentation tốt</li>
                    <li>❌ Cộng đồng nhỏ hơn React</li>
                </ul>
                
                <h3>3. Angular</h3>
                <p>Framework full-featured của Google.</p>
                <ul>
                    <li>✅ TypeScript built-in</li>
                    <li>✅ Powerful CLI</li>
                    <li>❌ Phức tạp cho beginners</li>
                </ul>
                
                <p>Bạn đang sử dụng framework nào? Chia sẻ kinh nghiệm nhé!</p>
                `,
                tags: ["javascript", "react", "vue", "angular", "frontend"],
                topicName: "Khoa học máy tính"
            },
            {
                title: "Kinh nghiệm phỏng vấn vị trí Software Engineer",
                content: `
                <h2>Chia sẻ kinh nghiệm phỏng vấn tại các công ty công nghệ</h2>
                
                <h3>Chuẩn bị trước phỏng vấn</h3>
                <ol>
                    <li><strong>Ôn tập kiến thức cơ bản:</strong> Data structures, Algorithms</li>
                    <li><strong>Luyện tập coding:</strong> LeetCode, HackerRank</li>
                    <li><strong>Chuẩn bị câu hỏi:</strong> Về công ty, team, dự án</li>
                </ol>
                
                <h3>Các vòng phỏng vấn thường gặp</h3>
                <ul>
                    <li>📞 Phone screening</li>
                    <li>💻 Technical coding</li>
                    <li>🏗️ System design</li>
                    <li>👥 Behavioral interview</li>
                </ul>
                
                <h3>Tips quan trọng</h3>
                <blockquote>
                    <p>"Đừng chỉ focus vào việc giải đúng, hãy explain thought process của bạn!"</p>
                </blockquote>
                
                <p>Chúc các bạn phỏng vấn thành công! 🚀</p>
                `,
                tags: ["phỏng vấn", "career", "software engineer", "tips"],
                topicName: "Khoa học máy tính"
            },
            
            // Posts cho "Học tập"
            {
                title: "Phương pháp Pomodoro - Bí quyết quản lý thời gian hiệu quả",
                content: `
                <h2>Kỹ thuật Pomodoro là gì?</h2>
                <p>Pomodoro là phương pháp quản lý thời gian được phát triển bởi Francesco Cirillo vào cuối những năm 1980.</p>
                
                <h3>Cách thực hiện</h3>
                <ol>
                    <li>⏰ Chọn một nhiệm vụ cần hoàn thành</li>
                    <li>🍅 Đặt timer 25 phút</li>
                    <li>🎯 Tập trung làm việc cho đến khi timer kêu</li>
                    <li>☑️ Đánh dấu hoàn thành 1 pomodoro</li>
                    <li>🛌 Nghỉ ngắn 5 phút</li>
                    <li>🔄 Lặp lại, sau 4 pomodoro nghỉ dài 15-30 phút</li>
                </ol>
                
                <h3>Lợi ích</h3>
                <ul>
                    <li>Tăng khả năng tập trung</li>
                    <li>Giảm stress và burnout</li>
                    <li>Cải thiện ước lượng thời gian</li>
                    <li>Tăng động lực làm việc</li>
                </ul>
                
                <h3>Apps hỗ trợ</h3>
                <p>📱 <strong>Mobile:</strong> Forest, Focus Keeper, PomoDone</p>
                <p>💻 <strong>Desktop:</strong> Toggl, Clockify, Be Focused</p>
                
                <p>Các bạn đã thử phương pháp này chưa? Chia sẻ kinh nghiệm nhé!</p>
                `,
                tags: ["pomodoro", "quản lý thời gian", "học tập", "productivity"],
                topicName: "Học tập"
            },
            {
                title: "Cách ghi chú hiệu quả bằng phương pháp Cornell Notes",
                content: `
                <h2>Cornell Note-Taking System</h2>
                <p>Được phát triển tại Đại học Cornell, đây là một trong những phương pháp ghi chú hiệu quả nhất.</p>
                
                <h3>Cấu trúc trang ghi chú</h3>
                <div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                    <div style="display: flex;">
                        <div style="width: 30%; border-right: 1px solid #ccc; padding-right: 10px;">
                            <strong>Cue Column</strong><br>
                            - Từ khóa<br>
                            - Câu hỏi<br>
                            - Gợi ý
                        </div>
                        <div style="width: 70%; padding-left: 10px;">
                            <strong>Note-taking Area</strong><br>
                            - Ghi chú chính<br>
                            - Chi tiết<br>
                            - Ví dụ
                        </div>
                    </div>
                    <div style="border-top: 1px solid #ccc; margin-top: 10px; padding-top: 10px;">
                        <strong>Summary Section</strong><br>
                        Tóm tắt nội dung chính của trang
                    </div>
                </div>
                
                <h3>Quy trình 5R</h3>
                <ol>
                    <li><strong>Record:</strong> Ghi chú trong giờ học</li>
                    <li><strong>Reduce:</strong> Rút gọn thành từ khóa</li>
                    <li><strong>Recite:</strong> Đọc lại và kiểm tra hiểu biết</li>
                    <li><strong>Reflect:</strong> Suy ngẫm và liên kết kiến thức</li>
                    <li><strong>Review:</strong> Ôn tập định kỳ</li>
                </ol>
                
                <h3>Ưu điểm</h3>
                <ul>
                    <li>📝 Cấu trúc rõ ràng</li>
                    <li>🧠 Kích thích tư duy phản biện</li>
                    <li>📚 Dễ ôn tập</li>
                    <li>⏱️ Tiết kiệm thời gian</li>
                </ul>
                
                <p>Hãy thử áp dụng và chia sẻ kết quả nhé!</p>
                `,
                tags: ["ghi chú", "cornell notes", "học tập", "study tips"],
                topicName: "Học tập"
            },
            
            // Posts cho "Sinh viên"
            {
                title: "Kinh nghiệm sống xa nhà - Dành cho tân sinh viên",
                content: `
                <h2>Lần đầu sống xa nhà? Đây là những điều bạn cần biết!</h2>
                
                <h3>🏠 Về chỗ ở</h3>
                <ul>
                    <li><strong>Ký túc xá:</strong> Rẻ, an toàn, dễ kết bạn</li>
                    <li><strong>Nhà trọ:</strong> Tự do hơn, cần chọn kỹ chủ trọ</li>
                    <li><strong>Thuê chung:</strong> Tiết kiệm, học cách sống chung</li>
                </ul>
                
                <h3>💰 Quản lý tài chính</h3>
                <ol>
                    <li>Lập budget hàng tháng</li>
                    <li>Ưu tiên: Ăn uống > Học tập > Giải trí</li>
                    <li>Tìm part-time job phù hợp</li>
                    <li>Tận dụng ưu đãi sinh viên</li>
                </ol>
                
                <h3>🍜 Về ăn uống</h3>
                <p><strong>Học nấu ăn cơ bản:</strong></p>
                <ul>
                    <li>Cơm rang trứng</li>
                    <li>Mì tôm nâng cấp</li>
                    <li>Salad đơn giản</li>
                    <li>Smoothie trái cây</li>
                </ul>
                
                <h3>🧺 Giặt giũ và vệ sinh</h3>
                <ul>
                    <li>Phân loại quần áo trước khi giặt</li>
                    <li>Dọn dẹp phòng hàng tuần</li>
                    <li>Mua sắm đồ dùng cần thiết</li>
                </ul>
                
                <h3>💡 Tips quan trọng</h3>
                <blockquote>
                    <p>"Đừng ngại hỏi han và kết bạn với mọi người xung quanh. Mạng lưới quan hệ rất quan trọng!"</p>
                </blockquote>
                
                <p>Chúc các bạn tân sinh viên có một năm học thành công! 🎓</p>
                `,
                tags: ["sinh viên", "sống xa nhà", "kinh nghiệm", "tân sinh viên"],
                topicName: "Sinh viên"
            },
            {
                title: "Top 10 ứng dụng không thể thiếu cho sinh viên",
                content: `
                <h2>📱 Danh sách apps hữu ích cho sinh viên</h2>
                
                <h3>📚 Học tập</h3>
                <ol>
                    <li><strong>Notion:</strong> Ghi chú, quản lý dự án all-in-one</li>
                    <li><strong>Anki:</strong> Flashcards thông minh</li>
                    <li><strong>Forest:</strong> Tập trung học tập</li>
                    <li><strong>Grammarly:</strong> Kiểm tra ngữ pháp tiếng Anh</li>
                </ol>
                
                <h3>💰 Tài chính</h3>
                <ol start="5">
                    <li><strong>Momo/ZaloPay:</strong> Thanh toán điện tử</li>
                    <li><strong>Money Lover:</strong> Quản lý chi tiêu</li>
                </ol>
                
                <h3>🚌 Di chuyển</h3>
                <ol start="7">
                    <li><strong>Google Maps:</strong> Chỉ đường, tìm địa điểm</li>
                    <li><strong>Grab:</strong> Đặt xe, giao đồ ăn</li>
                </ol>
                
                <h3>🎯 Tiện ích</h3>
                <ol start="9">
                    <li><strong>Google Drive:</strong> Lưu trữ đám mây</li>
                    <li><strong>Zalo:</strong> Liên lạc với bạn bè, gia đình</li>
                </ol>
                
                <h3>🎁 Bonus Apps</h3>
                <ul>
                    <li><strong>Spotify/YouTube Music:</strong> Nghe nhạc học tập</li>
                    <li><strong>Duolingo:</strong> Học ngoại ngữ</li>
                    <li><strong>CamScanner:</strong> Scan tài liệu</li>
                    <li><strong>Coursera/edX:</strong> Khóa học online miễn phí</li>
                </ul>
                
                <p>Các bạn còn app nào hay ho khác không? Share thêm nhé! 📲</p>
                `,
                tags: ["apps", "sinh viên", "công nghệ", "tiện ích"],
                topicName: "Sinh viên"
            }
        ];
        
        // Xóa posts cũ
        await Post.deleteMany({});
        console.log('🗑️ Đã xóa posts cũ');
        
        // Tạo posts mới
        let createdCount = 0;
        
        for (const postData of samplePostsData) {
            // Tìm topic theo tên
            const topic = topics.find(t => t.name === postData.topicName);
            if (!topic) {
                console.log(`⚠️ Không tìm thấy topic: ${postData.topicName}`);
                continue;
            }
            
            // Chọn random user làm author
            const author = users[Math.floor(Math.random() * users.length)];
            
            // Tạo post
            const post = new Post({
                title: postData.title,
                content: postData.content,
                authorId: author._id,
                topicId: topic._id,
                tags: postData.tags,
                views: Math.floor(Math.random() * 500) + 50, // 50-550 views
                commentCount: Math.floor(Math.random() * 20), // 0-20 comments
                likeCount: Math.floor(Math.random() * 50), // 0-50 likes
                ratingCount: Math.floor(Math.random() * 10), // 0-10 ratings
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random trong 30 ngày qua
            });
            
            await post.save();
            createdCount++;
            
            console.log(`✅ Tạo post: "${postData.title}" cho topic "${topic.name}"`);
        }
        
        console.log(`🎉 Đã tạo ${createdCount} sample posts thành công!`);
        
        // Hiển thị thống kê
        const totalPosts = await Post.countDocuments();
        console.log(`📊 Tổng số posts trong database: ${totalPosts}`);
        
        // Hiển thị posts theo topic
        for (const topic of topics) {
            const postCount = await Post.countDocuments({ topicId: topic._id });
            console.log(`   - ${topic.name}: ${postCount} posts`);
        }
        
    } catch (error) {
        console.error('❌ Lỗi khi tạo sample posts:', error);
    }
}

async function main() {
    await connectDB();
    await generateSamplePosts();
    
    console.log('\n✨ Hoàn thành tạo dữ liệu mẫu posts!');
    console.log('🔗 Bây giờ bạn có thể click vào topics từ Home page để xem posts');
    
    process.exit(0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = { generateSamplePosts };
