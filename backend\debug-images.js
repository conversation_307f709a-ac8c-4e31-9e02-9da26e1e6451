// Debug images in posts
require('dotenv').config();
const mongoose = require('mongoose');
const Post = require('./models/Post');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
};

const debugImages = async () => {
    await connectDB();
    
    console.log('\n🔍 Debugging Images in Posts...\n');
    
    try {
        const posts = await Post.find().select('title content images').limit(5);
        
        posts.forEach((post, index) => {
            console.log(`\n📝 Post ${index + 1}: ${post.title}`);
            console.log('Content preview:', post.content.substring(0, 300));
            console.log('Has img tags:', post.content.includes('<img'));
            
            // Extract img tags
            const imgMatches = post.content.match(/<img[^>]+>/g);
            if (imgMatches) {
                console.log('Image tags found:');
                imgMatches.forEach((img, i) => {
                    console.log(`  ${i + 1}. ${img}`);
                    
                    // Extract src
                    const srcMatch = img.match(/src=["']([^"']+)["']/);
                    if (srcMatch) {
                        console.log(`     URL: ${srcMatch[1]}`);
                    }
                });
            } else {
                console.log('No img tags found');
            }
            
            // Check images field
            if (post.images && post.images.length > 0) {
                console.log('Images field:', post.images);
            }
            
            console.log('-'.repeat(50));
        });
        
    } catch (error) {
        console.error('❌ Error debugging images:', error);
    }
    
    mongoose.connection.close();
};

debugImages();
