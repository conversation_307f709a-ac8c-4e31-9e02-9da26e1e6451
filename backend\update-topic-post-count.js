// File: backend/update-topic-post-count.js
// Script để cập nhật postCount cho tất cả topics

const mongoose = require('mongoose');
const Topic = require('./models/Topic');
const Post = require('./models/Post');

// Kết nối MongoDB
async function connectDB() {
    try {
        await mongoose.connect('mongodb://localhost:27017/dien_dan_TVU', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

// Cập nhật postCount cho tất cả topics
async function updateAllTopicPostCounts() {
    try {
        console.log('🔄 Bắt đầu cập nhật postCount cho tất cả topics...');
        
        // Lấy tất cả topics
        const topics = await Topic.find({});
        console.log(`📊 Tìm thấy ${topics.length} topics`);
        
        let updatedCount = 0;
        
        for (const topic of topics) {
            // Đếm số bài viết không bị xóa cho topic này
            const actualPostCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $ne: 'deleted' }
            });
            
            // Cập nhật nếu khác với postCount hiện tại
            if (topic.postCount !== actualPostCount) {
                await Topic.findByIdAndUpdate(topic._id, { postCount: actualPostCount });
                console.log(`✅ Cập nhật topic "${topic.name}": ${topic.postCount} → ${actualPostCount}`);
                updatedCount++;
            } else {
                console.log(`✓ Topic "${topic.name}": ${actualPostCount} (không thay đổi)`);
            }
        }
        
        console.log(`\n🎉 Hoàn thành! Đã cập nhật ${updatedCount}/${topics.length} topics`);
        
    } catch (error) {
        console.error('❌ Lỗi khi cập nhật postCount:', error);
    }
}

// Kiểm tra và hiển thị thống kê
async function showTopicStats() {
    try {
        console.log('\n📈 Thống kê topics sau khi cập nhật:');
        console.log('='.repeat(50));
        
        const topics = await Topic.find({}).sort({ postCount: -1 });
        
        for (const topic of topics) {
            // Verify bằng cách đếm lại
            const verifyCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $ne: 'deleted' }
            });
            
            const status = topic.postCount === verifyCount ? '✅' : '❌';
            console.log(`${status} ${topic.name}: ${topic.postCount} bài viết (verify: ${verifyCount})`);
        }
        
    } catch (error) {
        console.error('❌ Lỗi khi hiển thị thống kê:', error);
    }
}

// Main function
async function main() {
    await connectDB();
    
    const command = process.argv[2];
    
    switch (command) {
        case 'update':
            await updateAllTopicPostCounts();
            break;
        case 'stats':
            await showTopicStats();
            break;
        case 'all':
            await updateAllTopicPostCounts();
            await showTopicStats();
            break;
        default:
            console.log('📖 Cách sử dụng:');
            console.log('  node update-topic-post-count.js update  # Cập nhật postCount');
            console.log('  node update-topic-post-count.js stats   # Hiển thị thống kê');
            console.log('  node update-topic-post-count.js all     # Cập nhật và hiển thị thống kê');
            break;
    }
    
    await mongoose.disconnect();
    console.log('👋 Đã ngắt kết nối MongoDB');
}

// Chạy script
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { updateAllTopicPostCounts, showTopicStats };
