// File: backend/fix-topic-data.js
// Script để sửa dữ liệu chủ đề hiện có

const mongoose = require('mongoose');
const Topic = require('./models/Topic');
const Post = require('./models/Post');
require('dotenv').config();

// Kết nối database
async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

// Cập nhật các trường thiếu cho chủ đề hiện có
async function updateExistingTopics() {
    try {
        console.log('\n🔄 Đang cập nhật dữ liệu chủ đề hiện có...');

        const topics = await Topic.find({});
        console.log(`📊 Tìm thấy ${topics.length} chủ đề`);

        let updatedCount = 0;

        for (const topic of topics) {
            let needUpdate = false;
            const updateData = {};

            // Kiểm tra và thêm các trường thiếu
            if (!topic.category) {
                updateData.category = 'Học tập';
                needUpdate = true;
            }

            if (topic.priority === undefined || topic.priority === null) {
                updateData.priority = 0;
                needUpdate = true;
            }

            if (!topic.status) {
                updateData.status = 'active';
                needUpdate = true;
            }

            if (!topic.color) {
                updateData.color = '#1976d2';
                needUpdate = true;
            }

            if (!topic.icon) {
                updateData.icon = 'topic';
                needUpdate = true;
            }

            if (!topic.tags) {
                updateData.tags = [];
                needUpdate = true;
            }

            if (topic.isVisible === undefined || topic.isVisible === null) {
                updateData.isVisible = true;
                needUpdate = true;
            }

            if (topic.allowPosts === undefined || topic.allowPosts === null) {
                updateData.allowPosts = true;
                needUpdate = true;
            }

            if (topic.requireApproval === undefined || topic.requireApproval === null) {
                updateData.requireApproval = false;
                needUpdate = true;
            }

            if (topic.viewCount === undefined || topic.viewCount === null) {
                updateData.viewCount = 0;
                needUpdate = true;
            }

            // Cập nhật postCount
            const postCount = await Post.countDocuments({
                topicId: topic._id,
                status: { $in: ['published', 'pending', 'draft'] }
            });

            if (topic.postCount !== postCount) {
                updateData.postCount = postCount;
                needUpdate = true;
            }

            // Thêm createdAt nếu thiếu (sử dụng _id để ước tính thời gian)
            if (!topic.createdAt) {
                updateData.createdAt = topic._id.getTimestamp();
                needUpdate = true;
                console.log(`  📅 Thêm createdAt cho "${topic.name}": ${updateData.createdAt}`);
            }

            // Thêm updatedAt nếu thiếu
            if (!topic.updatedAt) {
                updateData.updatedAt = topic.createdAt || topic._id.getTimestamp();
                needUpdate = true;
                console.log(`  📅 Thêm updatedAt cho "${topic.name}": ${updateData.updatedAt}`);
            }

            if (needUpdate) {
                await Topic.findByIdAndUpdate(topic._id, updateData);
                updatedCount++;
                console.log(`✅ Đã cập nhật chủ đề: ${topic.name}`);
            }
        }

        console.log(`\n🎉 Hoàn thành! Đã cập nhật ${updatedCount}/${topics.length} chủ đề`);

    } catch (error) {
        console.error('❌ Lỗi khi cập nhật chủ đề:', error);
    }
}

// Tạo dữ liệu mẫu nếu chưa có chủ đề nào
async function createSampleTopics() {
    try {
        const existingTopics = await Topic.countDocuments();

        if (existingTopics > 0) {
            console.log(`📊 Đã có ${existingTopics} chủ đề trong database`);
            return;
        }

        console.log('\n🌱 Tạo dữ liệu mẫu...');

        const sampleTopics = [
            {
                name: 'Lập trình Web',
                description: 'Thảo luận về các công nghệ web hiện đại như React, Vue, Angular, Node.js',
                category: 'Công nghệ',
                priority: 9,
                color: '#2196f3',
                icon: 'code',
                tags: ['web', 'javascript', 'frontend', 'backend'],
                status: 'active',
                isVisible: true,
                allowPosts: true,
                requireApproval: false
            },
            {
                name: 'Thực tập doanh nghiệp',
                description: 'Chia sẻ kinh nghiệm thực tập và cơ hội việc làm cho sinh viên',
                category: 'Thực tập',
                priority: 8,
                color: '#ff9800',
                icon: 'work',
                tags: ['thực tập', 'việc làm', 'kinh nghiệm', 'doanh nghiệp'],
                status: 'active',
                isVisible: true,
                allowPosts: true,
                requireApproval: false
            },
            {
                name: 'Nghiên cứu khoa học',
                description: 'Các đề tài nghiên cứu, phương pháp khoa học và chia sẻ kết quả nghiên cứu',
                category: 'Nghiên cứu',
                priority: 7,
                color: '#9c27b0',
                icon: 'science',
                tags: ['nghiên cứu', 'khoa học', 'luận văn', 'đề tài'],
                status: 'active',
                isVisible: true,
                allowPosts: true,
                requireApproval: true
            },
            {
                name: 'Kỹ năng mềm',
                description: 'Phát triển kỹ năng giao tiếp, thuyết trình, làm việc nhóm và lãnh đạo',
                category: 'Kỹ năng mềm',
                priority: 6,
                color: '#4caf50',
                icon: 'psychology',
                tags: ['kỹ năng mềm', 'giao tiếp', 'thuyết trình', 'lãnh đạo'],
                status: 'active',
                isVisible: true,
                allowPosts: true,
                requireApproval: false
            },
            {
                name: 'Hoạt động sinh viên',
                description: 'Các hoạt động ngoại khóa, câu lạc bộ, sự kiện và phong trào sinh viên',
                category: 'Hoạt động sinh viên',
                priority: 5,
                color: '#e91e63',
                icon: 'groups',
                tags: ['hoạt động', 'câu lạc bộ', 'sự kiện', 'sinh viên'],
                status: 'active',
                isVisible: true,
                allowPosts: true,
                requireApproval: false
            }
        ];

        // Tạo admin user giả để làm createdBy
        const adminUser = await mongoose.connection.db.collection('users').findOne({ role: 'admin' });
        const createdBy = adminUser ? adminUser._id : new mongoose.Types.ObjectId();

        for (const topicData of sampleTopics) {
            topicData.createdBy = createdBy;
            topicData.postCount = 0;
            topicData.viewCount = 0;

            const topic = new Topic(topicData);
            await topic.save();
            console.log(`✅ Đã tạo chủ đề: ${topicData.name}`);
        }

        console.log(`\n🎉 Đã tạo ${sampleTopics.length} chủ đề mẫu`);

    } catch (error) {
        console.error('❌ Lỗi khi tạo dữ liệu mẫu:', error);
    }
}

// Hàm chính
async function main() {
    await connectDB();

    const args = process.argv.slice(2);

    if (args.includes('--create-samples')) {
        await createSampleTopics();
    } else {
        await updateExistingTopics();
    }

    console.log('\n✨ Hoàn thành tất cả tác vụ!');
    process.exit(0);
}

// Chạy script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    updateExistingTopics,
    createSampleTopics
};
