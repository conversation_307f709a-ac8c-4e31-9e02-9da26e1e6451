// File: backend/generate-sample-chatbot-data.js
// Script để tạo dữ liệu mẫu cho chatbot

const mongoose = require('mongoose');
const ChatbotIntent = require('./models/ChatbotIntent');
const ChatbotConversation = require('./models/ChatbotConversation');
const User = require('./models/User');
require('dotenv').config();

// Kết nối database
async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

// Tạo sample intents
async function generateSampleIntents() {
    try {
        console.log('\n🔄 Tạo sample intents...');
        
        // Lấy admin user
        const adminUser = await User.findOne({ role: 'admin' });
        if (!adminUser) {
            console.log('❌ Không tìm thấy admin user');
            return;
        }

        const sampleIntents = [
            {
                name: 'greeting',
                displayName: 'Chào hỏi',
                description: 'Intent xử lý các câu chào hỏi',
                category: 'greeting',
                trainingPhrases: [
                    { text: 'xin chào' },
                    { text: 'hello' },
                    { text: 'chào bạn' },
                    { text: 'hi' },
                    { text: 'chào buổi sáng' },
                    { text: 'chào buổi chiều' }
                ],
                responses: [
                    { type: 'text', text: 'Xin chào! Tôi có thể giúp gì cho bạn?' },
                    { type: 'text', text: 'Chào bạn! Bạn cần hỗ trợ gì không?' },
                    { type: 'text', text: 'Hello! Tôi là chatbot hỗ trợ của diễn đàn TVU.' }
                ],
                status: 'active',
                createdBy: adminUser._id
            },
            {
                name: 'how_to_register',
                displayName: 'Hướng dẫn đăng ký',
                description: 'Hướng dẫn cách đăng ký tài khoản',
                category: 'faq',
                trainingPhrases: [
                    { text: 'làm sao để đăng ký' },
                    { text: 'cách đăng ký tài khoản' },
                    { text: 'đăng ký như thế nào' },
                    { text: 'tôi muốn tạo tài khoản' },
                    { text: 'hướng dẫn đăng ký' }
                ],
                responses: [
                    { 
                        type: 'text', 
                        text: 'Để đăng ký tài khoản, bạn click vào nút "Đăng ký" ở góc trên bên phải, sau đó điền đầy đủ thông tin và xác nhận email.' 
                    }
                ],
                status: 'active',
                createdBy: adminUser._id
            },
            {
                name: 'how_to_post',
                displayName: 'Hướng dẫn đăng bài',
                description: 'Hướng dẫn cách đăng bài viết',
                category: 'faq',
                trainingPhrases: [
                    { text: 'làm sao để đăng bài' },
                    { text: 'cách đăng bài viết' },
                    { text: 'tôi muốn đăng bài' },
                    { text: 'hướng dẫn tạo bài viết' },
                    { text: 'post bài như thế nào' }
                ],
                responses: [
                    { 
                        type: 'text', 
                        text: 'Để đăng bài viết, bạn cần đăng nhập, sau đó click vào nút "Tạo bài viết", chọn chủ đề phù hợp và viết nội dung bài của bạn.' 
                    }
                ],
                status: 'active',
                createdBy: adminUser._id
            },
            {
                name: 'contact_support',
                displayName: 'Liên hệ hỗ trợ',
                description: 'Thông tin liên hệ hỗ trợ',
                category: 'support',
                trainingPhrases: [
                    { text: 'liên hệ admin' },
                    { text: 'tôi cần hỗ trợ' },
                    { text: 'báo cáo lỗi' },
                    { text: 'contact support' },
                    { text: 'gặp vấn đề' }
                ],
                responses: [
                    { 
                        type: 'text', 
                        text: 'Bạn có thể liên hệ với admin qua email: <EMAIL> hoặc gửi tin nhắn trực tiếp trong diễn đàn.' 
                    }
                ],
                status: 'active',
                createdBy: adminUser._id
            },
            {
                name: 'forum_rules',
                displayName: 'Quy định diễn đàn',
                description: 'Thông tin về quy định của diễn đàn',
                category: 'information',
                trainingPhrases: [
                    { text: 'quy định diễn đàn' },
                    { text: 'luật lệ' },
                    { text: 'điều khoản sử dụng' },
                    { text: 'rules' },
                    { text: 'những gì không được phép' }
                ],
                responses: [
                    { 
                        type: 'text', 
                        text: 'Diễn đàn có các quy định cơ bản: không spam, không nội dung xấu, tôn trọng người khác, đăng đúng chủ đề. Xem chi tiết tại mục "Quy định".' 
                    }
                ],
                status: 'active',
                createdBy: adminUser._id
            },
            {
                name: 'popular_topics',
                displayName: 'Chủ đề phổ biến',
                description: 'Thông tin về các chủ đề phổ biến',
                category: 'information',
                trainingPhrases: [
                    { text: 'chủ đề nào hot' },
                    { text: 'chủ đề phổ biến' },
                    { text: 'topic nào nhiều người quan tâm' },
                    { text: 'trending topics' },
                    { text: 'bài viết nào hay' }
                ],
                responses: [
                    { 
                        type: 'text', 
                        text: 'Các chủ đề phổ biến hiện tại bao gồm: Khoa học máy tính, Học tập, Sinh viên, Công nghệ. Bạn có thể xem thêm ở trang chủ.' 
                    }
                ],
                status: 'active',
                createdBy: adminUser._id
            },
            {
                name: 'goodbye',
                displayName: 'Tạm biệt',
                description: 'Xử lý lời chào tạm biệt',
                category: 'greeting',
                trainingPhrases: [
                    { text: 'tạm biệt' },
                    { text: 'bye' },
                    { text: 'goodbye' },
                    { text: 'hẹn gặp lại' },
                    { text: 'cảm ơn' }
                ],
                responses: [
                    { type: 'text', text: 'Tạm biệt! Chúc bạn một ngày tốt lành!' },
                    { type: 'text', text: 'Hẹn gặp lại bạn! Có gì cần hỗ trợ hãy quay lại nhé.' },
                    { type: 'text', text: 'Bye bye! Cảm ơn bạn đã sử dụng diễn đàn TVU.' }
                ],
                status: 'active',
                createdBy: adminUser._id
            }
        ];

        // Xóa intents cũ
        await ChatbotIntent.deleteMany({});

        // Tạo intents mới
        for (const intentData of sampleIntents) {
            const intent = new ChatbotIntent(intentData);
            await intent.save();
        }

        console.log(`✅ Đã tạo ${sampleIntents.length} sample intents`);
        
    } catch (error) {
        console.error('❌ Lỗi khi tạo sample intents:', error);
    }
}

// Tạo sample conversations
async function generateSampleConversations() {
    try {
        console.log('\n🔄 Tạo sample conversations...');
        
        const users = await User.find({}).limit(5);
        const intents = await ChatbotIntent.find({});
        
        if (users.length === 0 || intents.length === 0) {
            console.log('❌ Không có users hoặc intents để tạo conversations');
            return;
        }

        const deviceTypes = ['desktop', 'mobile', 'tablet'];
        const conversations = [];
        const now = new Date();

        // Tạo conversations cho 7 ngày qua
        for (let day = 0; day < 7; day++) {
            const date = new Date(now);
            date.setDate(date.getDate() - day);
            
            // Tạo 3-8 conversations mỗi ngày
            const conversationsPerDay = Math.floor(Math.random() * 5) + 3;
            
            for (let i = 0; i < conversationsPerDay; i++) {
                const user = Math.random() > 0.3 ? users[Math.floor(Math.random() * users.length)] : null;
                const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                
                const startedAt = new Date(date);
                startedAt.setHours(Math.floor(Math.random() * 24));
                startedAt.setMinutes(Math.floor(Math.random() * 60));
                
                // Tạo messages
                const messages = [];
                const messageCount = Math.floor(Math.random() * 8) + 2; // 2-10 messages
                
                for (let j = 0; j < messageCount; j++) {
                    if (j % 2 === 0) {
                        // User message
                        const intent = intents[Math.floor(Math.random() * intents.length)];
                        const trainingPhrase = intent.trainingPhrases[Math.floor(Math.random() * intent.trainingPhrases.length)];
                        
                        messages.push({
                            type: 'user',
                            text: trainingPhrase.text,
                            detectedIntent: {
                                name: intent.name,
                                displayName: intent.displayName,
                                confidence: Math.random() * 0.4 + 0.6 // 0.6-1.0
                            },
                            timestamp: new Date(startedAt.getTime() + j * 30000), // 30s apart
                            responseTime: Math.floor(Math.random() * 200) + 100
                        });
                    } else {
                        // Bot message
                        const intent = intents[Math.floor(Math.random() * intents.length)];
                        const response = intent.responses[Math.floor(Math.random() * intent.responses.length)];
                        
                        messages.push({
                            type: 'bot',
                            text: response.text,
                            botResponse: {
                                responseType: 'text',
                                fulfillmentText: response.text
                            },
                            timestamp: new Date(startedAt.getTime() + j * 30000 + 5000), // 5s after user
                            responseTime: Math.floor(Math.random() * 1000) + 500
                        });
                    }
                }
                
                const endedAt = new Date(messages[messages.length - 1].timestamp.getTime() + 10000);
                const duration = Math.floor((endedAt - startedAt) / 1000);
                
                // Tạo feedback ngẫu nhiên (30% có feedback)
                const feedback = Math.random() < 0.3 ? {
                    rating: Math.floor(Math.random() * 5) + 1,
                    comment: Math.random() < 0.5 ? 'Chatbot hữu ích' : '',
                    feedbackAt: endedAt
                } : undefined;
                
                const conversation = {
                    sessionId,
                    userId: user?._id,
                    sessionInfo: {
                        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
                        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        deviceType: deviceTypes[Math.floor(Math.random() * deviceTypes.length)],
                        platform: 'web',
                        language: 'vi'
                    },
                    messages,
                    status: 'ended',
                    startedAt,
                    endedAt,
                    duration,
                    feedback,
                    needsReview: feedback?.rating <= 2 || false,
                    reviewed: false
                };
                
                conversations.push(conversation);
            }
        }

        // Xóa conversations cũ
        await ChatbotConversation.deleteMany({});

        // Tạo conversations mới
        for (const conversationData of conversations) {
            const conversation = new ChatbotConversation(conversationData);
            conversation.updateStats();
            await conversation.save();
        }

        console.log(`✅ Đã tạo ${conversations.length} sample conversations`);
        
    } catch (error) {
        console.error('❌ Lỗi khi tạo sample conversations:', error);
    }
}

// Cập nhật stats cho intents
async function updateIntentStats() {
    try {
        console.log('\n🔄 Cập nhật intent stats...');
        
        const intents = await ChatbotIntent.find({});
        
        for (const intent of intents) {
            // Đếm số lần intent được trigger
            const triggerCount = await ChatbotConversation.aggregate([
                { $unwind: '$messages' },
                {
                    $match: {
                        'messages.type': 'user',
                        'messages.detectedIntent.name': intent.name
                    }
                },
                { $count: 'total' }
            ]);
            
            const count = triggerCount[0]?.total || 0;
            
            // Tính confidence trung bình
            const avgConfidenceResult = await ChatbotConversation.aggregate([
                { $unwind: '$messages' },
                {
                    $match: {
                        'messages.type': 'user',
                        'messages.detectedIntent.name': intent.name
                    }
                },
                {
                    $group: {
                        _id: null,
                        avgConfidence: { $avg: '$messages.detectedIntent.confidence' }
                    }
                }
            ]);
            
            const avgConfidence = avgConfidenceResult[0]?.avgConfidence || 0;
            
            // Cập nhật stats
            intent.stats.triggerCount = count;
            intent.stats.successCount = Math.floor(count * 0.8); // 80% success rate
            intent.stats.avgConfidence = avgConfidence;
            intent.stats.lastTriggered = count > 0 ? new Date() : undefined;
            
            await intent.save();
        }
        
        console.log(`✅ Đã cập nhật stats cho ${intents.length} intents`);
        
    } catch (error) {
        console.error('❌ Lỗi khi cập nhật intent stats:', error);
    }
}

// Hàm chính
async function main() {
    await connectDB();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--intents-only')) {
        await generateSampleIntents();
    } else if (args.includes('--conversations-only')) {
        await generateSampleConversations();
    } else if (args.includes('--stats-only')) {
        await updateIntentStats();
    } else {
        console.log('🚀 Tạo tất cả dữ liệu mẫu cho chatbot...');
        await generateSampleIntents();
        await generateSampleConversations();
        await updateIntentStats();
    }
    
    console.log('\n✨ Hoàn thành tạo dữ liệu mẫu chatbot!');
    process.exit(0);
}

// Chạy script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    generateSampleIntents,
    generateSampleConversations,
    updateIntentStats
};
