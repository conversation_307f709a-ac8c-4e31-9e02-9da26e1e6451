// Simple script to check posts count
require('dotenv').config();

async function checkPosts() {
    try {
        console.log('Checking posts...');
        
        // Use direct MongoDB connection
        const { MongoClient } = require('mongodb');
        const client = new MongoClient(process.env.MONGO_URI);
        
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db();
        
        // Count posts
        const postsCount = await db.collection('posts').countDocuments();
        console.log(`📊 Total posts: ${postsCount}`);
        
        // Count topics
        const topicsCount = await db.collection('topics').countDocuments();
        console.log(`📂 Total topics: ${topicsCount}`);
        
        // Get sample posts
        const samplePosts = await db.collection('posts').find({}).limit(10).toArray();
        console.log('\n📝 Sample posts:');
        samplePosts.forEach((post, index) => {
            console.log(`${index + 1}. ${post.title}`);
        });
        
        if (postsCount > 10) {
            console.log(`... and ${postsCount - 10} more posts`);
        }
        
        // Posts by topic
        console.log('\n📊 Posts by topic:');
        const topics = await db.collection('topics').find({}).toArray();
        
        for (const topic of topics) {
            const count = await db.collection('posts').countDocuments({ topicId: topic._id });
            console.log(`  - ${topic.name}: ${count} posts`);
        }
        
        await client.close();
        console.log('\n✅ Check completed');
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

checkPosts();
