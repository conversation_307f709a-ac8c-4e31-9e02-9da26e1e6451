# <PERSON><PERSON> thống Quản lý Chủ đề cho Admin

## Tổng quan
Hệ thống quản lý chủ đề cho admin cho phép quản trị viên:
- Th<PERSON><PERSON>, sửa, xóa chủ đề phù hợp với nhu cầu học tập và xu hướng sinh viên
- Quản lý danh mục và phân loại chủ đề
- Thiết lập thứ tự ưu tiên hiển thị
- Kiểm soát quyền tạo bài viết và duyệt nội dung
- Xem thống kê và phân tích chủ đề

## Các file đã tạo/chỉnh sửa

### Backend

1. **backend/models/Topic.js** - Đ<PERSON> cập nhật hoàn toàn
   - Thêm các trường: `category`, `priority`, `status`, `color`, `icon`, `imageUrl`, `tags`
   - Thêm các trường quản lý: `createdBy`, `updatedBy`, `isVisible`, `allowPosts`, `requireApproval`
   - Thêm thống kê: `postCount`, `viewCount`
   - Thêm các index, virtual, static methods và instance methods

2. **backend/controllers/adminTopicController.js** - Mới
   - `getAllTopics()` - Lấy danh sách chủ đề với phân trang và tìm kiếm
   - `getTopicById()` - Lấy thông tin chi tiết chủ đề
   - `createTopic()` - Tạo chủ đề mới
   - `updateTopic()` - Cập nhật thông tin chủ đề
   - `deleteTopic()` - Xóa chủ đề (với kiểm tra ràng buộc)
   - `updateTopicStatus()` - Thay đổi trạng thái chủ đề
   - `bulkUpdatePriority()` - Cập nhật thứ tự ưu tiên hàng loạt
   - `getTopicStats()` - Lấy thống kê chủ đề

3. **backend/routes/adminTopicRoutes.js** - Mới
   - Định nghĩa các routes cho quản lý chủ đề
   - Áp dụng middleware xác thực và phân quyền admin

4. **backend/index.js** - Đã cập nhật
   - Thêm import và route cho adminTopicRoutes

### Frontend

5. **frontend/src/pages/admin/AdminTopicsPage.jsx** - Mới
   - Giao diện quản lý chủ đề hoàn chỉnh
   - Bảng hiển thị với tìm kiếm, lọc, phân trang
   - Dialog tạo/chỉnh sửa chủ đề
   - Dialog xác nhận xóa
   - Thống kê chủ đề với cards

6. **frontend/src/layouts/AdminDashboard.jsx** - Đã cập nhật
   - Thêm import và route cho AdminTopicsPage

7. **frontend/src/pages/admin/Sidebar.jsx** - Đã cập nhật
   - Thêm menu item "Quản lý Chủ đề"

### Test

8. **backend/test-admin-topics.js** - Mới
   - Script test các API quản lý chủ đề
   - Hàm tạo dữ liệu mẫu

## API Endpoints

### Quản lý chủ đề (Admin only)

```
GET    /api/admin/topics/stats                    - Lấy thống kê chủ đề
PUT    /api/admin/topics/bulk-update-priority     - Cập nhật thứ tự ưu tiên hàng loạt
GET    /api/admin/topics                          - Lấy danh sách chủ đề
POST   /api/admin/topics                          - Tạo chủ đề mới
GET    /api/admin/topics/:id                      - Lấy thông tin chi tiết chủ đề
PUT    /api/admin/topics/:id                      - Cập nhật thông tin chủ đề
DELETE /api/admin/topics/:id                      - Xóa chủ đề
PUT    /api/admin/topics/:id/status               - Thay đổi trạng thái chủ đề
```

## Cấu trúc Model Topic

### Các trường chính:
- **name**: Tên chủ đề (bắt buộc, duy nhất)
- **description**: Mô tả chi tiết (bắt buộc)
- **category**: Danh mục (Học tập, Nghiên cứu, Thực tập, v.v.)
- **priority**: Thứ tự ưu tiên (0-10)
- **status**: Trạng thái (active, inactive, archived)
- **color**: Màu sắc hiển thị (hex color)
- **icon**: Icon đại diện
- **imageUrl**: Ảnh đại diện
- **tags**: Thẻ tags liên quan
- **isVisible**: Hiển thị công khai
- **allowPosts**: Cho phép tạo bài viết
- **requireApproval**: Yêu cầu duyệt bài viết

### Thống kê:
- **postCount**: Số lượng bài viết
- **viewCount**: Số lượt xem

### Quản lý:
- **createdBy**: Người tạo
- **updatedBy**: Người cập nhật cuối
- **createdAt**: Ngày tạo
- **updatedAt**: Ngày cập nhật

## Danh mục chủ đề

1. **Học tập** - Các chủ đề liên quan đến học tập, môn học
2. **Nghiên cứu** - Đề tài nghiên cứu, luận văn, khoa học
3. **Thực tập** - Kinh nghiệm thực tập, cơ hội thực tập
4. **Việc làm** - Tuyển dụng, cơ hội nghề nghiệp
5. **Hoạt động sinh viên** - Sự kiện, câu lạc bộ, hoạt động ngoại khóa
6. **Công nghệ** - Công nghệ mới, lập trình, IT
7. **Kỹ năng mềm** - Kỹ năng giao tiếp, thuyết trình, làm việc nhóm
8. **Trao đổi học thuật** - Thảo luận học thuật, chia sẻ kiến thức
9. **Thông báo** - Thông báo chính thức từ trường
10. **Khác** - Các chủ đề khác

## Cách sử dụng

### 1. Khởi động server
```bash
cd backend
npm start
```

### 2. Khởi động frontend
```bash
cd frontend
npm run dev
```

### 3. Truy cập trang admin
- Đăng nhập với tài khoản admin
- Truy cập `/admin/topics` để quản lý chủ đề

### 4. Các chức năng chính

#### Xem danh sách chủ đề
- Hiển thị bảng với thông tin cơ bản
- Tìm kiếm theo tên, mô tả hoặc tags
- Lọc theo danh mục và trạng thái
- Sắp xếp theo thứ tự ưu tiên
- Phân trang

#### Tạo chủ đề mới
- Click nút "+" để mở dialog tạo mới
- Điền thông tin bắt buộc: tên và mô tả
- Chọn danh mục, màu sắc, icon
- Thiết lập các tùy chọn hiển thị và quyền

#### Chỉnh sửa chủ đề
- Click icon "Chỉnh sửa" để mở dialog
- Cập nhật thông tin cần thiết
- Lưu thay đổi

#### Quản lý trạng thái
- **Hoạt động**: Chủ đề hiển thị và cho phép tạo bài viết
- **Không hoạt động**: Chủ đề ẩn nhưng vẫn giữ dữ liệu
- **Đã lưu trữ**: Chủ đề được lưu trữ, không hiển thị

#### Xóa chủ đề
- Click icon "Xóa" để mở dialog xác nhận
- Chỉ có thể xóa khi không có bài viết nào sử dụng
- Hành động không thể hoàn tác

#### Cập nhật thứ tự ưu tiên
- Sử dụng chức năng bulk update để thay đổi thứ tự hiển thị
- Chủ đề có priority cao hơn sẽ hiển thị trước

## Tính năng nâng cao

### 1. Thống kê chủ đề
- Tổng số chủ đề theo trạng thái
- Phân bố theo danh mục
- Chủ đề phổ biến nhất
- Chủ đề mới tạo gần đây

### 2. Tìm kiếm và lọc
- Tìm kiếm full-text trong tên, mô tả, tags
- Lọc theo danh mục
- Lọc theo trạng thái
- Sắp xếp theo nhiều tiêu chí

### 3. Quản lý quyền
- Kiểm soát ai có thể tạo bài viết trong chủ đề
- Thiết lập yêu cầu duyệt bài viết
- Ẩn/hiện chủ đề với người dùng

## Test và Debug

### Chạy test API:
```bash
cd backend
node test-admin-topics.js
```

### Tạo dữ liệu mẫu:
```bash
cd backend
node test-admin-topics.js --create-samples
```

### Debug số lượng bài viết:
```bash
cd backend
# Debug tất cả
node debug-post-count.js

# Debug chỉ chủ đề cụ thể
node debug-post-count.js --debug-specific

# Debug tất cả chủ đề
node debug-post-count.js --debug-all

# Sửa lỗi postCount
node debug-post-count.js --fix
```

### Sửa dữ liệu hiện có:
```bash
cd backend
node fix-topic-data.js
```

## Lưu ý quan trọng

1. **Backup dữ liệu**: Luôn backup trước khi thực hiện thay đổi lớn
2. **Kiểm tra ràng buộc**: Không thể xóa chủ đề đang có bài viết
3. **Thứ tự ưu tiên**: Số càng cao càng hiển thị trước
4. **Màu sắc**: Sử dụng hex color code (#rrggbb)
5. **Tags**: Phân cách bằng dấu phẩy, tự động trim khoảng trắng

## Troubleshooting

### Lỗi thường gặp:
1. **Tên chủ đề đã tồn tại**: Mỗi chủ đề phải có tên duy nhất
2. **Không thể xóa chủ đề**: Kiểm tra xem có bài viết nào đang sử dụng không
3. **Lỗi quyền truy cập**: Đảm bảo đăng nhập với tài khoản admin
4. **Lỗi validation**: Kiểm tra các trường bắt buộc và định dạng dữ liệu

### Debug:
- Kiểm tra console browser để xem lỗi frontend
- Kiểm tra log server để xem lỗi backend
- Sử dụng Network tab để debug API calls
