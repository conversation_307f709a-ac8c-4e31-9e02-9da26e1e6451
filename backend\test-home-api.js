// Test Home API endpoints
require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');
const Post = require('./models/Post');
const Topic = require('./models/Topic');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
};

const testHomeAPI = async () => {
    await connectDB();

    console.log('\n🔍 Testing Home API Data...\n');

    try {
        // Test user count
        const userCount = await User.countDocuments({ role: 'user' });
        console.log(`👥 Users with role 'user': ${userCount}`);

        // Test post count
        const postCount = await Post.countDocuments();
        console.log(`📝 Total posts: ${postCount}`);

        // Test topic count
        const topicCount = await Topic.countDocuments();
        console.log(`📚 Total topics: ${topicCount}`);

        // Test featured posts
        const featuredPosts = await Post.find({ featured: true }).limit(5);
        console.log(`⭐ Featured posts: ${featuredPosts.length}`);

        // Test trending topics
        const trendingTopics = await Topic.find({ trending: true }).limit(5);
        console.log(`🔥 Trending topics: ${trendingTopics.length}`);

        // Show sample data
        console.log('\n📊 Sample Data:');

        if (postCount > 0) {
            const samplePost = await Post.findOne()
                .populate('authorId', 'fullName username')
                .populate('topicId', 'name color');
            console.log('Sample Post:', {
                title: samplePost?.title,
                author: samplePost?.authorId?.fullName,
                topic: samplePost?.topicId?.name,
                featured: samplePost?.featured
            });
        }

        if (topicCount > 0) {
            const sampleTopic = await Topic.findOne();
            console.log('Sample Topic:', {
                name: sampleTopic?.name,
                category: sampleTopic?.category,
                trending: sampleTopic?.trending
            });
        }

    } catch (error) {
        console.error('❌ Error testing data:', error);
    }

    mongoose.connection.close();
};

const addSampleData = async () => {
    await connectDB();

    console.log('\n🚀 Adding sample data...\n');

    try {
        // Add sample users if none exist
        const userCount = await User.countDocuments({ role: 'user' });
        if (userCount === 0) {
            const sampleUsers = [
                {
                    fullName: 'Nguyễn Văn A',
                    username: 'nguyenvana',
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'user'
                },
                {
                    fullName: 'Trần Thị B',
                    username: 'tranthib',
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'user'
                },
                {
                    fullName: 'Lê Văn C',
                    username: 'levanc',
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'user'
                }
            ];

            await User.insertMany(sampleUsers);
            console.log('✅ Added sample users');
        }

        // Add sample topics if none exist
        const topicCount = await Topic.countDocuments();
        if (topicCount === 0) {
            const sampleTopics = [
                {
                    name: 'Học tập & Nghiên cứu',
                    description: 'Thảo luận về học tập và nghiên cứu khoa học',
                    category: 'academic',
                    color: '#2196F3',
                    trending: true,
                    status: 'active',
                    isVisible: true
                },
                {
                    name: 'Đời sống Sinh viên',
                    description: 'Chia sẻ về cuộc sống sinh viên',
                    category: 'social',
                    color: '#FF9800',
                    trending: true,
                    status: 'active',
                    isVisible: true
                },
                {
                    name: 'Tuyển dụng & Việc làm',
                    description: 'Thông tin việc làm và cơ hội nghề nghiệp',
                    category: 'career',
                    color: '#9C27B0',
                    trending: false,
                    status: 'active',
                    isVisible: true
                }
            ];

            await Topic.insertMany(sampleTopics);
            console.log('✅ Added sample topics');
        }

        // Add sample posts if none exist
        const postCount = await Post.countDocuments();
        if (postCount === 0) {
            const users = await User.find({ role: 'user' }).limit(3);
            const topics = await Topic.find().limit(3);

            if (users.length > 0 && topics.length > 0) {
                const samplePosts = [
                    {
                        title: 'Làm sao để học tốt kỳ này và không bị stress?',
                        content: 'Chia sẻ những phương pháp học tập hiệu quả và cách quản lý stress trong học tập...',
                        authorId: users[0]._id,
                        topicId: topics[0]._id,
                        featured: true,
                        status: 'published',
                        views: 245,
                        images: ['https://picsum.photos/400/250?random=1']
                    },
                    {
                        title: 'Top 5 địa điểm giải trí "chill" nhất Trà Vinh!',
                        content: 'Khám phá những địa điểm thú vị và phù hợp với sinh viên tại Trà Vinh...',
                        authorId: users[1]._id,
                        topicId: topics[1]._id,
                        featured: true,
                        status: 'published',
                        views: 189,
                        images: ['https://picsum.photos/400/250?random=2']
                    },
                    {
                        title: 'Kinh nghiệm tìm kiếm việc làm thêm cho sinh viên',
                        content: 'Chia sẻ kinh nghiệm và mẹo hay để tìm được công việc part-time phù hợp...',
                        authorId: users[2]._id,
                        topicId: topics[2]._id,
                        featured: false,
                        status: 'published',
                        views: 298,
                        images: ['https://picsum.photos/400/250?random=3']
                    }
                ];

                await Post.insertMany(samplePosts);
                console.log('✅ Added sample posts');
            }
        }

        console.log('\n🎉 Sample data setup complete!');

    } catch (error) {
        console.error('❌ Error adding sample data:', error);
    }

    mongoose.connection.close();
};

// Run based on command line argument
const command = process.argv[2];

if (command === 'test') {
    testHomeAPI();
} else if (command === 'seed') {
    addSampleData();
} else {
    console.log('Usage:');
    console.log('  node test-home-api.js test  - Test current data');
    console.log('  node test-home-api.js seed  - Add sample data');
}
