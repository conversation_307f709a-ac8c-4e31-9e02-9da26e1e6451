// File: backend/test-analytics.js
// Script test cho analytics APIs

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = 'http://localhost:5000/api';

// Thông tin admin để test
const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'admin123'
};

let authToken = '';

// Tạo axios instance
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000
});

// Interceptor để thêm token
api.interceptors.request.use(config => {
    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
});

// Đăng nhập admin
async function loginAdmin() {
    console.log('\n=== Đăng nhập Admin ===');
    try {
        const response = await api.post('/auth/login', ADMIN_CREDENTIALS);
        if (response.data.success && response.data.token) {
            authToken = response.data.token;
            console.log('✅ Đăng nhập thành công');
            return true;
        } else {
            console.log('❌ Đăng nhập thất bại:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Lỗi đăng nhập:', error.response?.data?.message || error.message);
        return false;
    }
}

// Test overview stats
async function testOverviewStats() {
    console.log('\n=== Test: Overview Stats ===');
    try {
        const response = await api.get('/admin/analytics/overview?days=30');
        console.log('✅ Success:', {
            totals: response.data.data.totals,
            period: response.data.data.period,
            growth: response.data.data.growth
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test user activity stats
async function testUserActivityStats() {
    console.log('\n=== Test: User Activity Stats ===');
    try {
        const response = await api.get('/admin/analytics/user-activity?days=30');
        console.log('✅ Success:', {
            activityStatsCount: response.data.data.activityStats?.length || 0,
            hourlyActivityCount: response.data.data.hourlyActivity?.length || 0,
            dailyActivityCount: response.data.data.dailyActivity?.length || 0,
            topActiveUsersCount: response.data.data.topActiveUsers?.length || 0
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test popular content stats
async function testPopularContentStats() {
    console.log('\n=== Test: Popular Content Stats ===');
    try {
        const response = await api.get('/admin/analytics/popular-content?days=30');
        console.log('✅ Success:', {
            popularPostsCount: response.data.data.popularPosts?.length || 0,
            popularTopicsCount: response.data.data.popularTopics?.length || 0,
            categoryStatsCount: response.data.data.categoryStats?.length || 0
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test search analytics
async function testSearchAnalytics() {
    console.log('\n=== Test: Search Analytics ===');
    try {
        const response = await api.get('/admin/analytics/search-analytics?days=30');
        console.log('✅ Success:', {
            overview: response.data.data.overview,
            popularSearchesCount: response.data.data.popularSearches?.length || 0,
            searchTrendsCount: response.data.data.searchTrends?.length || 0,
            failedSearchesCount: response.data.data.failedSearches?.length || 0,
            deviceStatsCount: response.data.data.deviceStats?.length || 0
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test growth trends
async function testGrowthTrends() {
    console.log('\n=== Test: Growth Trends ===');
    try {
        const response = await api.get('/admin/analytics/growth-trends?days=30');
        console.log('✅ Success:', {
            userGrowthCount: response.data.data.userGrowth?.length || 0,
            postGrowthCount: response.data.data.postGrowth?.length || 0,
            commentGrowthCount: response.data.data.commentGrowth?.length || 0
        });
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test log user activity
async function testLogUserActivity() {
    console.log('\n=== Test: Log User Activity ===');
    try {
        const activityData = {
            activityType: 'view_post',
            details: {
                targetId: '507f1f77bcf86cd799439011',
                targetType: 'post',
                metadata: { postTitle: 'Test Post' }
            },
            sessionInfo: {
                ipAddress: '*************',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                deviceType: 'desktop',
                browser: 'Chrome',
                os: 'Windows'
            }
        };
        
        const response = await api.post('/admin/analytics/log-activity', activityData);
        console.log('✅ Success:', response.data.message);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test log search
async function testLogSearch() {
    console.log('\n=== Test: Log Search ===');
    try {
        const searchData = {
            query: 'javascript tutorial',
            searchType: 'posts',
            results: {
                count: 15,
                hasResults: true,
                processingTime: 120
            },
            filters: {
                category: 'Programming',
                sortBy: 'relevance'
            },
            sessionInfo: {
                ipAddress: '*************',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                deviceType: 'desktop'
            }
        };
        
        const response = await api.post('/admin/analytics/log-search', searchData);
        console.log('✅ Success:', response.data.message, 'SearchID:', response.data.searchId);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Test với các time periods khác nhau
async function testDifferentTimePeriods() {
    console.log('\n=== Test: Different Time Periods ===');
    const periods = [7, 30, 90, 365];
    
    for (const days of periods) {
        try {
            console.log(`\n--- Testing ${days} days ---`);
            const response = await api.get(`/admin/analytics/overview?days=${days}`);
            console.log(`✅ ${days} days:`, {
                newUsers: response.data.data.period.newUsers,
                newPosts: response.data.data.period.newPosts,
                activeUsers: response.data.data.period.activeUsers
            });
        } catch (error) {
            console.log(`❌ ${days} days error:`, error.response?.data?.message || error.message);
        }
    }
}

// Test performance
async function testPerformance() {
    console.log('\n=== Test: Performance ===');
    
    const endpoints = [
        '/admin/analytics/overview',
        '/admin/analytics/user-activity',
        '/admin/analytics/popular-content',
        '/admin/analytics/search-analytics',
        '/admin/analytics/growth-trends'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const startTime = Date.now();
            await api.get(`${endpoint}?days=30`);
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`✅ ${endpoint}: ${duration}ms`);
            
            if (duration > 2000) {
                console.log(`⚠️  Warning: ${endpoint} took ${duration}ms (>2s)`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint} error:`, error.response?.data?.message || error.message);
        }
    }
}

// Hàm chính
async function main() {
    console.log('🚀 Bắt đầu test Analytics APIs...');
    
    // Đăng nhập
    const loginSuccess = await loginAdmin();
    if (!loginSuccess) {
        console.log('❌ Không thể đăng nhập. Dừng test.');
        return;
    }
    
    const args = process.argv.slice(2);
    
    if (args.includes('--overview-only')) {
        await testOverviewStats();
    } else if (args.includes('--activity-only')) {
        await testUserActivityStats();
    } else if (args.includes('--content-only')) {
        await testPopularContentStats();
    } else if (args.includes('--search-only')) {
        await testSearchAnalytics();
    } else if (args.includes('--growth-only')) {
        await testGrowthTrends();
    } else if (args.includes('--log-only')) {
        await testLogUserActivity();
        await testLogSearch();
    } else if (args.includes('--performance')) {
        await testPerformance();
    } else if (args.includes('--periods')) {
        await testDifferentTimePeriods();
    } else {
        // Test tất cả
        await testOverviewStats();
        await testUserActivityStats();
        await testPopularContentStats();
        await testSearchAnalytics();
        await testGrowthTrends();
        await testLogUserActivity();
        await testLogSearch();
        await testDifferentTimePeriods();
        await testPerformance();
    }
    
    console.log('\n✨ Hoàn thành test Analytics APIs!');
}

// Chạy test
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    testOverviewStats,
    testUserActivityStats,
    testPopularContentStats,
    testSearchAnalytics,
    testGrowthTrends,
    testLogUserActivity,
    testLogSearch,
    testDifferentTimePeriods,
    testPerformance
};
