// Simple debug script
require('dotenv').config();
const mongoose = require('mongoose');

async function debug() {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected!');
        
        const Topic = require('./models/Topic');
        const Post = require('./models/Post');
        
        // Check topics
        const topics = await Topic.find({}).limit(3);
        console.log('\n=== TOPICS ===');
        console.log(`Found ${topics.length} topics`);
        topics.forEach(topic => {
            console.log(`- ${topic.name} (${topic._id})`);
        });
        
        // Check posts
        const posts = await Post.find({}).limit(3);
        console.log('\n=== POSTS ===');
        console.log(`Found ${posts.length} posts`);
        posts.forEach(post => {
            console.log(`- ${post.title} (Topic: ${post.topicId})`);
        });
        
        // Test specific topic
        if (topics.length > 0) {
            const firstTopic = topics[0];
            console.log(`\n=== POSTS FOR TOPIC: ${firstTopic.name} ===`);
            const topicPosts = await Post.find({ topicId: firstTopic._id });
            console.log(`Found ${topicPosts.length} posts for this topic`);
            
            // Test API endpoint
            console.log(`\n=== TEST API ===`);
            console.log(`URL: http://localhost:5000/api/posts/topic-details/${firstTopic._id}`);
        }
        
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

debug();
