// Script to check and ensure database consistency
require('dotenv').config();
const mongoose = require('mongoose');
const { MongoClient } = require('mongodb');

const checkDatabaseConsistency = async () => {
    console.log('🔍 Checking Database Consistency...\n');
    
    // Check .env configuration
    console.log('📋 Environment Configuration:');
    console.log(`   MONGO_URI: ${process.env.MONGO_URI}`);
    console.log(`   Expected DB: dien_dan_TVU`);
    
    // Extract database name from MONGO_URI
    const dbName = process.env.MONGO_URI.split('/').pop();
    console.log(`   Actual DB from URI: ${dbName}\n`);
    
    if (dbName !== 'dien_dan_TVU') {
        console.log('❌ WARNING: Database name mismatch!');
        console.log(`   Expected: dien_dan_TVU`);
        console.log(`   Found: ${dbName}`);
        console.log('   Please update MONGO_URI in .env file\n');
    } else {
        console.log('✅ Database name is correct: dien_dan_TVU\n');
    }
    
    try {
        // Connect using MongoClient to list all databases
        const client = new MongoClient(process.env.MONGO_URI.replace(`/${dbName}`, ''));
        await client.connect();
        
        console.log('📊 Available Databases:');
        const adminDb = client.db().admin();
        const databases = await adminDb.listDatabases();
        
        let foundDienDanTVU = false;
        let foundHiluAuau = false;
        
        databases.databases.forEach(db => {
            console.log(`   - ${db.name} (${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
            
            if (db.name === 'dien_dan_TVU') {
                foundDienDanTVU = true;
            }
            if (db.name === 'hilu-auau') {
                foundHiluAuau = true;
            }
        });
        
        console.log('\n🎯 Database Analysis:');
        console.log(`   ✅ dien_dan_TVU exists: ${foundDienDanTVU ? 'YES' : 'NO'}`);
        console.log(`   ⚠️  hilu-auau exists: ${foundHiluAuau ? 'YES' : 'NO'}`);
        
        if (foundHiluAuau && foundDienDanTVU) {
            console.log('\n🔄 Both databases exist! Checking data...');
            
            // Check data in both databases
            const dienDanDB = client.db('dien_dan_TVU');
            const hiluAuauDB = client.db('hilu-auau');
            
            // Count collections in both databases
            const dienDanCollections = await dienDanDB.listCollections().toArray();
            const hiluAuauCollections = await hiluAuauDB.listCollections().toArray();
            
            console.log(`\n📊 dien_dan_TVU collections: ${dienDanCollections.length}`);
            dienDanCollections.forEach(col => console.log(`   - ${col.name}`));
            
            console.log(`\n📊 hilu-auau collections: ${hiluAuauCollections.length}`);
            hiluAuauCollections.forEach(col => console.log(`   - ${col.name}`));
            
            // Count documents in key collections
            const keyCollections = ['users', 'posts', 'topics', 'comments'];
            
            console.log('\n📈 Document Counts Comparison:');
            console.log('Collection'.padEnd(15) + 'dien_dan_TVU'.padEnd(15) + 'hilu-auau');
            console.log('-'.repeat(45));
            
            for (const collectionName of keyCollections) {
                try {
                    const dienDanCount = await dienDanDB.collection(collectionName).countDocuments();
                    const hiluAuauCount = await hiluAuauDB.collection(collectionName).countDocuments();
                    
                    console.log(
                        collectionName.padEnd(15) + 
                        dienDanCount.toString().padEnd(15) + 
                        hiluAuauCount.toString()
                    );
                } catch (error) {
                    console.log(`${collectionName.padEnd(15)}ERROR           ERROR`);
                }
            }
            
            console.log('\n💡 Recommendations:');
            console.log('   1. Choose one database as the primary (recommended: dien_dan_TVU)');
            console.log('   2. Migrate data if needed');
            console.log('   3. Update all scripts to use the same database');
            console.log('   4. Drop the unused database to avoid confusion');
        }
        
        await client.close();
        
    } catch (error) {
        console.error('❌ Error checking databases:', error);
    }
    
    // Check Mongoose connection
    console.log('\n🔗 Testing Mongoose Connection...');
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Mongoose connected successfully');
        
        // Test basic operations
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log(`📋 Collections in current database: ${collections.length}`);
        collections.forEach(col => console.log(`   - ${col.name}`));
        
        mongoose.connection.close();
        
    } catch (error) {
        console.error('❌ Mongoose connection failed:', error);
    }
};

const fixDatabaseReferences = async () => {
    console.log('\n🔧 Checking for hardcoded database references...\n');
    
    const fs = require('fs');
    const path = require('path');
    
    const filesToCheck = [
        'scripts/createAdminUser.js',
        'scripts/generateAnalyticsData.js',
        '../DEBUG_DASHBOARD_ISSUES.md'
    ];
    
    let foundIssues = false;
    
    for (const filePath of filesToCheck) {
        const fullPath = path.join(__dirname, filePath);
        
        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');
            
            if (content.includes('hilu-auau')) {
                console.log(`❌ Found 'hilu-auau' reference in: ${filePath}`);
                foundIssues = true;
            } else {
                console.log(`✅ Clean: ${filePath}`);
            }
        } else {
            console.log(`⚠️  File not found: ${filePath}`);
        }
    }
    
    if (!foundIssues) {
        console.log('\n✅ All files are using correct database references!');
    } else {
        console.log('\n❌ Some files still have incorrect database references.');
        console.log('   Please update them to use process.env.MONGO_URI');
    }
};

const main = async () => {
    console.log('🚀 Database Consistency Checker\n');
    console.log('='.repeat(50));
    
    await checkDatabaseConsistency();
    await fixDatabaseReferences();
    
    console.log('\n' + '='.repeat(50));
    console.log('✨ Check completed!');
    
    process.exit(0);
};

main().catch(console.error);
