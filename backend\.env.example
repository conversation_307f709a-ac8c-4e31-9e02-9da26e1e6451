# Database
MONGO_URI=mongodb://localhost:27017/dien_dan_tvu

# JWT
JWT_SECRET=your_jwt_secret_key_here

# Server
PORT=5000

# Dialogflow Configuration
# 1. Tạo Google Cloud Project tại: https://console.cloud.google.com/
# 2. Enable Dialogflow API
# 3. Tạo Service Account và download JSON key file
# 4. Đi<PERSON>n thông tin bên dưới

# Project ID từ Google Cloud Console
DIALOGFLOW_PROJECT_ID=your-dialogflow-project-id

# Đường dẫn đến file JSON key của Service Account
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Ngôn ngữ mặc định (vi cho tiếng Việt)
DIALOGFLOW_LANGUAGE_CODE=vi

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Other configurations
NODE_ENV=development
