const mongoose = require('mongoose');
const NotificationService = require('./services/notificationService');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/dien_dan_TVU', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const testNotification = async () => {
    try {
        console.log('🧪 Testing notification URL format...');

        // Create notification service instance
        const notificationService = new NotificationService();

        // Test post liked notification
        console.log('📝 Testing post liked notification...');
        await notificationService.notifyPostLiked(
            '684af68ec09109df9b158e54', // postId
            '684aeaf725a448854a923466', // postAuthorId
            'Test User', // likerName
            'Test Post Title', // postTitle
            '6814aecb2238577c20bb8ca4' // topicId
        );

        // Test comment added notification
        console.log('💬 Testing comment added notification...');
        await notificationService.notifyCommentAdded(
            '684af8c708d76e73f688b881', // commentId
            '684af68ec09109df9b158e54', // postId
            '684aeaf725a448854a923466', // postAuthorId
            'Test Commenter', // commenterName
            'Test Post Title', // postTitle
            '6814aecb2238577c20bb8ca4' // topicId
        );

        // Test admin notifications
        console.log('👨‍💼 Testing admin notifications...');
        await notificationService.notifyPostCreated(
            '684af68ec09109df9b158e54', // postId
            '684aeaf725a448854a923466', // authorId
            'Test Admin Post', // postTitle
            'Test Topic' // topicName
        );

        console.log('✅ Test notifications created successfully!');
        console.log('🔍 Check the database to see the actionUrl format');

        process.exit(0);
    } catch (error) {
        console.error('❌ Error creating test notifications:', error);
        process.exit(1);
    }
};

testNotification();
