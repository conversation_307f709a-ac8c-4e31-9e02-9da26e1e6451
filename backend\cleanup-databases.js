// Script to cleanup duplicate databases
require('dotenv').config();
const { MongoClient } = require('mongodb');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

const question = (query) => {
    return new Promise(resolve => rl.question(query, resolve));
};

const cleanupDatabases = async () => {
    console.log('🧹 Database Cleanup Tool\n');
    
    const baseUri = process.env.MONGO_URI.replace(/\/[^\/]*$/, '');
    
    try {
        const client = new MongoClient(baseUri);
        await client.connect();
        console.log('✅ Connected to MongoDB\n');
        
        // Get database info
        const dienDanDB = client.db('dien_dan_TVU');
        const hiluDB = client.db('hilu-auau');
        
        console.log('📊 Current Database Status:');
        
        // Check dien_dan_TVU
        try {
            const dienDanUsers = await dienDanDB.collection('users').countDocuments();
            const dienDanPosts = await dienDanDB.collection('posts').countDocuments();
            const dienDanTopics = await dienDanDB.collection('topics').countDocuments();
            
            console.log(`   dien_dan_TVU: ${dienDanUsers} users, ${dienDanPosts} posts, ${dienDanTopics} topics`);
        } catch (e) {
            console.log(`   dien_dan_TVU: Error reading data`);
        }
        
        // Check hilu-auau
        try {
            const hiluUsers = await hiluDB.collection('users').countDocuments();
            const hiluPosts = await hiluDB.collection('posts').countDocuments();
            const hiluTopics = await hiluDB.collection('topics').countDocuments();
            
            console.log(`   hilu-auau: ${hiluUsers} users, ${hiluPosts} posts, ${hiluTopics} topics`);
        } catch (e) {
            console.log(`   hilu-auau: Error reading data`);
        }
        
        console.log('\n🎯 Current Configuration:');
        console.log(`   .env MONGO_URI points to: dien_dan_TVU ✅`);
        console.log(`   Application is using: dien_dan_TVU`);
        
        console.log('\n💡 Options:');
        console.log('   1. Keep both databases (no action)');
        console.log('   2. Drop hilu-auau database (recommended)');
        console.log('   3. Migrate data from hilu-auau to dien_dan_TVU');
        console.log('   4. Exit without changes');
        
        const choice = await question('\nChoose an option (1-4): ');
        
        switch (choice) {
            case '1':
                console.log('\n✅ No changes made. Both databases preserved.');
                break;
                
            case '2':
                console.log('\n⚠️  WARNING: This will permanently delete the hilu-auau database!');
                const confirm = await question('Type "DELETE" to confirm: ');
                
                if (confirm === 'DELETE') {
                    await hiluDB.dropDatabase();
                    console.log('✅ hilu-auau database has been dropped.');
                    console.log('🎉 Database cleanup completed!');
                } else {
                    console.log('❌ Deletion cancelled.');
                }
                break;
                
            case '3':
                console.log('\n🔄 Migration option selected...');
                console.log('⚠️  This is a complex operation that requires careful planning.');
                console.log('💡 Recommendation: Manually review data differences first.');
                console.log('📝 Consider exporting important data before migration.');
                
                const proceedMigration = await question('Do you want to proceed with migration? (y/N): ');
                
                if (proceedMigration.toLowerCase() === 'y') {
                    console.log('🚧 Migration feature not implemented in this script.');
                    console.log('📋 Manual steps:');
                    console.log('   1. Export data from hilu-auau');
                    console.log('   2. Review for conflicts with dien_dan_TVU');
                    console.log('   3. Import non-conflicting data');
                    console.log('   4. Update references and IDs');
                } else {
                    console.log('❌ Migration cancelled.');
                }
                break;
                
            case '4':
                console.log('\n👋 Exiting without changes.');
                break;
                
            default:
                console.log('\n❌ Invalid option selected.');
                break;
        }
        
        await client.close();
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
    
    rl.close();
};

const verifyConsistency = async () => {
    console.log('\n🔍 Verifying system consistency...\n');
    
    // Check if all files use process.env.MONGO_URI
    const fs = require('fs');
    const path = require('path');
    
    const filesToCheck = [
        'scripts/createAdminUser.js',
        'scripts/generateAnalyticsData.js'
    ];
    
    let allGood = true;
    
    for (const filePath of filesToCheck) {
        const fullPath = path.join(__dirname, filePath);
        
        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');
            
            if (content.includes('hilu-auau')) {
                console.log(`❌ ${filePath} still contains 'hilu-auau'`);
                allGood = false;
            } else if (content.includes('process.env.MONGO_URI')) {
                console.log(`✅ ${filePath} uses process.env.MONGO_URI`);
            } else {
                console.log(`⚠️  ${filePath} may have hardcoded connection`);
                allGood = false;
            }
        }
    }
    
    if (allGood) {
        console.log('\n✅ All files are using correct database configuration!');
    } else {
        console.log('\n❌ Some files need to be updated.');
    }
    
    console.log('\n📋 Summary:');
    console.log('   - Primary database: dien_dan_TVU');
    console.log('   - Configuration: .env MONGO_URI');
    console.log('   - All scripts should use: process.env.MONGO_URI');
    console.log('   - Recommendation: Drop hilu-auau if not needed');
};

const main = async () => {
    await verifyConsistency();
    await cleanupDatabases();
    
    console.log('\n✨ Cleanup completed!');
    process.exit(0);
};

main().catch(console.error);
