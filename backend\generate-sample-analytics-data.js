// File: backend/generate-sample-analytics-data.js
// Script để tạo dữ liệu mẫu cho analytics

const mongoose = require('mongoose');
const UserActivity = require('./models/UserActivity');
const SearchLog = require('./models/SearchLog');
const User = require('./models/User');
const Post = require('./models/Post');
const Topic = require('./models/Topic');
require('dotenv').config();

// Kết nối database
async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

// Tạo dữ liệu hoạt động người dùng mẫu
async function generateUserActivities() {
    try {
        console.log('\n🔄 Tạo dữ liệu hoạt động người dùng...');
        
        const users = await User.find({}).limit(10);
        const posts = await Post.find({}).limit(20);
        const topics = await Topic.find({}).limit(10);
        
        if (users.length === 0) {
            console.log('❌ Không có người dùng nào trong database');
            return;
        }
        
        const activityTypes = [
            'login', 'logout', 'view_post', 'create_post', 'edit_post',
            'comment', 'like', 'search', 'view_topic', 'page_view'
        ];
        
        const deviceTypes = ['desktop', 'mobile', 'tablet'];
        const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
        const oses = ['Windows', 'macOS', 'Linux', 'Android', 'iOS'];
        
        const activities = [];
        const now = new Date();
        
        // Tạo hoạt động cho 30 ngày qua
        for (let day = 0; day < 30; day++) {
            const date = new Date(now);
            date.setDate(date.getDate() - day);
            
            // Tạo 10-50 hoạt động mỗi ngày
            const activitiesPerDay = Math.floor(Math.random() * 40) + 10;
            
            for (let i = 0; i < activitiesPerDay; i++) {
                const user = users[Math.floor(Math.random() * users.length)];
                const activityType = activityTypes[Math.floor(Math.random() * activityTypes.length)];
                
                // Tạo timestamp ngẫu nhiên trong ngày
                const timestamp = new Date(date);
                timestamp.setHours(Math.floor(Math.random() * 24));
                timestamp.setMinutes(Math.floor(Math.random() * 60));
                
                let details = {};
                
                // Tạo details dựa trên loại hoạt động
                if (activityType === 'view_post' && posts.length > 0) {
                    const post = posts[Math.floor(Math.random() * posts.length)];
                    details = {
                        targetId: post._id,
                        targetType: 'post',
                        metadata: { postTitle: post.title }
                    };
                } else if (activityType === 'view_topic' && topics.length > 0) {
                    const topic = topics[Math.floor(Math.random() * topics.length)];
                    details = {
                        targetId: topic._id,
                        targetType: 'topic',
                        metadata: { topicName: topic.name }
                    };
                } else if (activityType === 'search') {
                    const queries = ['javascript', 'react', 'nodejs', 'mongodb', 'css', 'html', 'python', 'java'];
                    details = {
                        metadata: {
                            query: queries[Math.floor(Math.random() * queries.length)],
                            resultCount: Math.floor(Math.random() * 20)
                        }
                    };
                }
                
                const activity = {
                    userId: user._id,
                    activityType,
                    details,
                    sessionInfo: {
                        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
                        userAgent: `Mozilla/5.0 (${oses[Math.floor(Math.random() * oses.length)]}) ${browsers[Math.floor(Math.random() * browsers.length)]}`,
                        deviceType: deviceTypes[Math.floor(Math.random() * deviceTypes.length)],
                        browser: browsers[Math.floor(Math.random() * browsers.length)],
                        os: oses[Math.floor(Math.random() * oses.length)]
                    },
                    timestamp
                };
                
                activities.push(activity);
            }
        }
        
        // Xóa dữ liệu cũ
        await UserActivity.deleteMany({});
        
        // Chèn dữ liệu mới
        await UserActivity.insertMany(activities);
        
        console.log(`✅ Đã tạo ${activities.length} hoạt động người dùng`);
        
    } catch (error) {
        console.error('❌ Lỗi khi tạo dữ liệu hoạt động:', error);
    }
}

// Tạo dữ liệu tìm kiếm mẫu
async function generateSearchLogs() {
    try {
        console.log('\n🔄 Tạo dữ liệu tìm kiếm...');
        
        const users = await User.find({}).limit(10);
        
        const searchQueries = [
            'javascript tutorial',
            'react hooks',
            'nodejs express',
            'mongodb aggregation',
            'css flexbox',
            'html5 semantic',
            'python django',
            'java spring boot',
            'vue.js components',
            'angular routing',
            'typescript interfaces',
            'docker containers',
            'git commands',
            'sql queries',
            'api design',
            'web security',
            'responsive design',
            'machine learning',
            'data structures',
            'algorithms'
        ];
        
        const searchTypes = ['posts', 'topics', 'users', 'global'];
        const deviceTypes = ['desktop', 'mobile', 'tablet'];
        
        const searchLogs = [];
        const now = new Date();
        
        // Tạo tìm kiếm cho 30 ngày qua
        for (let day = 0; day < 30; day++) {
            const date = new Date(now);
            date.setDate(date.getDate() - day);
            
            // Tạo 5-20 tìm kiếm mỗi ngày
            const searchesPerDay = Math.floor(Math.random() * 15) + 5;
            
            for (let i = 0; i < searchesPerDay; i++) {
                const query = searchQueries[Math.floor(Math.random() * searchQueries.length)];
                const hasResults = Math.random() > 0.2; // 80% tìm kiếm có kết quả
                const resultCount = hasResults ? Math.floor(Math.random() * 50) + 1 : 0;
                
                // Tạo timestamp ngẫu nhiên trong ngày
                const searchedAt = new Date(date);
                searchedAt.setHours(Math.floor(Math.random() * 24));
                searchedAt.setMinutes(Math.floor(Math.random() * 60));
                
                const searchLog = {
                    userId: users.length > 0 && Math.random() > 0.3 ? 
                        users[Math.floor(Math.random() * users.length)]._id : null, // 30% tìm kiếm ẩn danh
                    query,
                    searchType: searchTypes[Math.floor(Math.random() * searchTypes.length)],
                    results: {
                        count: resultCount,
                        hasResults,
                        processingTime: Math.floor(Math.random() * 500) + 50 // 50-550ms
                    },
                    sessionInfo: {
                        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
                        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        deviceType: deviceTypes[Math.floor(Math.random() * deviceTypes.length)]
                    },
                    searchedAt
                };
                
                searchLogs.push(searchLog);
            }
        }
        
        // Xóa dữ liệu cũ
        await SearchLog.deleteMany({});
        
        // Chèn dữ liệu mới
        await SearchLog.insertMany(searchLogs);
        
        console.log(`✅ Đã tạo ${searchLogs.length} log tìm kiếm`);
        
    } catch (error) {
        console.error('❌ Lỗi khi tạo dữ liệu tìm kiếm:', error);
    }
}

// Cập nhật view count cho posts và topics
async function updateViewCounts() {
    try {
        console.log('\n🔄 Cập nhật view count...');
        
        const posts = await Post.find({});
        const topics = await Topic.find({});
        
        // Cập nhật view count cho posts
        for (const post of posts) {
            const viewCount = Math.floor(Math.random() * 1000) + 10;
            await Post.findByIdAndUpdate(post._id, { views: viewCount });
        }
        
        // Cập nhật view count cho topics
        for (const topic of topics) {
            const viewCount = Math.floor(Math.random() * 5000) + 100;
            await Topic.findByIdAndUpdate(topic._id, { viewCount });
        }
        
        console.log(`✅ Đã cập nhật view count cho ${posts.length} bài viết và ${topics.length} chủ đề`);
        
    } catch (error) {
        console.error('❌ Lỗi khi cập nhật view count:', error);
    }
}

// Hàm chính
async function main() {
    await connectDB();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--activities-only')) {
        await generateUserActivities();
    } else if (args.includes('--search-only')) {
        await generateSearchLogs();
    } else if (args.includes('--views-only')) {
        await updateViewCounts();
    } else {
        console.log('🚀 Tạo tất cả dữ liệu mẫu cho analytics...');
        await generateUserActivities();
        await generateSearchLogs();
        await updateViewCounts();
    }
    
    console.log('\n✨ Hoàn thành tạo dữ liệu mẫu!');
    process.exit(0);
}

// Chạy script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = {
    generateUserActivities,
    generateSearchLogs,
    updateViewCounts
};
