// Create posts for correct topic names
const mongoose = require('mongoose');
require('dotenv').config();

const Post = require('./models/Post');
const Topic = require('./models/Topic');
const User = require('./models/User');

async function createPostsForCorrectTopics() {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to MongoDB');

        // Get all topics and users
        const topics = await Topic.find({});
        const users = await User.find({});

        console.log('📋 Available topics:');
        topics.forEach(topic => {
            console.log(`  - ${topic.name} (${topic._id})`);
        });

        // Sample posts for actual topic names
        const postsData = {
            "Khoa học máy tính": [
                {
                    title: "Hướng dẫn học Python từ cơ bản đến nâng cao",
                    content: "<h2>Python - Ngôn ngữ lập trình thân thiện</h2><p>Python là một trong những ngôn ngữ lập trình phổ biến nhất hiện nay.</p><h3>Tại sao nên học Python?</h3><ul><li>Cú pháp đơn giản, dễ hiểu</li><li>Thư viện phong phú</li><li>Cộng đồng lớn</li></ul>",
                    tags: ["python", "lập trình", "tutorial"]
                },
                {
                    title: "So sánh React vs Vue.js vs Angular",
                    content: "<h2>Framework JavaScript nào phù hợp?</h2><p>Trong thế giới frontend development, có 3 framework chính.</p><h3>React</h3><p>✅ Cộng đồng lớn<br>❌ Learning curve cao</p>",
                    tags: ["javascript", "react", "vue", "angular"]
                },
                {
                    title: "Kinh nghiệm phỏng vấn Software Engineer",
                    content: "<h2>Chia sẻ kinh nghiệm phỏng vấn</h2><p>Sau 5 lần phỏng vấn tại các công ty công nghệ.</p><h3>Chuẩn bị</h3><ol><li>Ôn tập Algorithms</li><li>Luyện LeetCode</li></ol>",
                    tags: ["phỏng vấn", "career", "tips"]
                }
            ],
            "Lập trình web": [
                {
                    title: "HTML5 và CSS3 - Nền tảng của Web Development",
                    content: "<h2>Bắt đầu với HTML5 và CSS3</h2><p>HTML5 và CSS3 là nền tảng của mọi website hiện đại.</p><h3>HTML5 mới có gì?</h3><ul><li>Semantic elements</li><li>Canvas API</li><li>Local Storage</li></ul>",
                    tags: ["html5", "css3", "web development"]
                },
                {
                    title: "Node.js vs PHP - Lựa chọn backend nào?",
                    content: "<h2>So sánh Node.js và PHP</h2><p>Cả hai đều là lựa chọn phổ biến cho backend development.</p><h3>Node.js</h3><p>✅ JavaScript full-stack<br>✅ Performance cao</p><h3>PHP</h3><p>✅ Dễ học<br>✅ Hosting rẻ</p>",
                    tags: ["nodejs", "php", "backend"]
                }
            ],
            "Trí tuệ nhân tạo": [
                {
                    title: "Machine Learning cơ bản cho người mới bắt đầu",
                    content: "<h2>Giới thiệu Machine Learning</h2><p>ML là một nhánh của AI, cho phép máy tính học từ dữ liệu.</p><h3>Các loại ML</h3><ul><li>Supervised Learning</li><li>Unsupervised Learning</li><li>Reinforcement Learning</li></ul>",
                    tags: ["machine learning", "AI", "data science"]
                },
                {
                    title: "ChatGPT và tương lai của AI",
                    content: "<h2>Cuộc cách mạng AI</h2><p>ChatGPT đã thay đổi cách chúng ta tương tác với AI.</p><h3>Ứng dụng</h3><ul><li>Viết code</li><li>Dịch thuật</li><li>Sáng tạo nội dung</li></ul>",
                    tags: ["chatgpt", "AI", "automation"]
                }
            ],
            "Công nghệ thông tin": [
                {
                    title: "Cloud Computing - Xu hướng tương lai",
                    content: "<h2>Điện toán đám mây</h2><p>Cloud computing đang thay đổi cách chúng ta sử dụng công nghệ.</p><h3>Lợi ích</h3><ul><li>Tiết kiệm chi phí</li><li>Scalability</li><li>Accessibility</li></ul>",
                    tags: ["cloud computing", "AWS", "Azure"]
                },
                {
                    title: "Cybersecurity - Bảo mật trong thời đại số",
                    content: "<h2>Tầm quan trọng của bảo mật</h2><p>Với sự phát triển của công nghệ, bảo mật trở nên quan trọng hơn bao giờ hết.</p><h3>Các mối đe dọa</h3><ul><li>Malware</li><li>Phishing</li><li>Data breach</li></ul>",
                    tags: ["cybersecurity", "security", "privacy"]
                }
            ],
            "Đời sống sinh viên": [
                {
                    title: "Kinh nghiệm sống xa nhà cho tân sinh viên",
                    content: "<h2>Lần đầu sống xa nhà?</h2><p>Đây là những điều bạn cần biết khi bước vào đời sống sinh viên.</p><h3>Về chỗ ở</h3><ul><li>Ký túc xá: Rẻ, an toàn</li><li>Nhà trọ: Tự do hơn</li></ul>",
                    tags: ["sinh viên", "sống xa nhà", "kinh nghiệm"]
                },
                {
                    title: "Cách quản lý tài chính sinh viên hiệu quả",
                    content: "<h2>Quản lý tiền bạc thông minh</h2><p>Học cách quản lý tài chính từ khi còn là sinh viên.</p><h3>Tips</h3><ol><li>Lập budget</li><li>Tiết kiệm</li><li>Tìm part-time</li></ol>",
                    tags: ["tài chính", "sinh viên", "tiết kiệm"]
                }
            ],
            "Việc làm và thực tập": [
                {
                    title: "Cách viết CV ấn tượng cho sinh viên IT",
                    content: "<h2>CV là chìa khóa thành công</h2><p>Một CV tốt sẽ giúp bạn có được cơ hội phỏng vấn.</p><h3>Cấu trúc CV</h3><ol><li>Thông tin cá nhân</li><li>Học vấn</li><li>Kinh nghiệm</li><li>Kỹ năng</li></ol>",
                    tags: ["CV", "việc làm", "career"]
                },
                {
                    title: "Top 10 công ty công nghệ tuyển thực tập sinh",
                    content: "<h2>Cơ hội thực tập tại các công ty lớn</h2><p>Danh sách các công ty đang tuyển thực tập sinh IT.</p><h3>Danh sách</h3><ol><li>FPT Software</li><li>VNG</li><li>Tiki</li><li>Shopee</li></ol>",
                    tags: ["thực tập", "internship", "công ty"]
                }
            ],
            "Câu hỏi thảo luận": [
                {
                    title: "Thảo luận: Ngôn ngữ lập trình nào nên học đầu tiên?",
                    content: "<h2>Cuộc tranh luận vĩnh cửu</h2><p>Ngôn ngữ lập trình đầu tiên sẽ ảnh hưởng đến tư duy lập trình của bạn.</p><h3>Các lựa chọn phổ biến</h3><ul><li>Python - Dễ học</li><li>Java - Cơ bản vững</li><li>JavaScript - Thực tế</li></ul>",
                    tags: ["thảo luận", "lập trình", "beginner"]
                },
                {
                    title: "Học đại học có còn cần thiết trong ngành IT?",
                    content: "<h2>Câu hỏi gây tranh cãi</h2><p>Với sự phát triển của các khóa học online, liệu bằng đại học có còn quan trọng?</p><h3>Quan điểm</h3><ul><li>Ủng hộ: Nền tảng vững chắc</li><li>Phản đối: Thực tế quan trọng hơn</li></ul>",
                    tags: ["thảo luận", "giáo dục", "career"]
                }
            ]
        };

        // Delete existing posts
        await Post.deleteMany({});
        console.log('🗑️ Deleted existing posts');

        let totalCreated = 0;

        // Create posts for each topic
        for (const topic of topics) {
            const topicPosts = postsData[topic.name] || [];
            
            if (topicPosts.length === 0) {
                console.log(`⚠️ No posts data for topic: ${topic.name}`);
                continue;
            }

            console.log(`\n📝 Creating posts for: ${topic.name}`);

            for (const postData of topicPosts) {
                // Random author
                const author = users[Math.floor(Math.random() * users.length)];
                
                const post = new Post({
                    title: postData.title,
                    content: postData.content,
                    authorId: author._id,
                    topicId: topic._id,
                    tags: postData.tags,
                    views: Math.floor(Math.random() * 500) + 50,
                    commentCount: Math.floor(Math.random() * 20),
                    likeCount: Math.floor(Math.random() * 50),
                    ratingCount: Math.floor(Math.random() * 10),
                    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
                });

                await post.save();
                console.log(`  ✅ ${postData.title}`);
                totalCreated++;
            }
        }

        console.log(`\n🎉 Created ${totalCreated} posts successfully!`);

        // Show statistics
        console.log('\n📊 Posts by topic:');
        for (const topic of topics) {
            const count = await Post.countDocuments({ topicId: topic._id });
            console.log(`  - ${topic.name}: ${count} posts`);
        }

        console.log('\n🔗 Test URLs:');
        for (const topic of topics) {
            const count = await Post.countDocuments({ topicId: topic._id });
            if (count > 0) {
                console.log(`  - ${topic.name}: http://localhost:5000/api/posts/topic-details/${topic._id}`);
            }
        }

        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

createPostsForCorrectTopics();
