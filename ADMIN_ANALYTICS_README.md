# <PERSON><PERSON> thống Thống kê và Phân tích cho Admin

## Tổng quan
Hệ thống thống kê và phân tích hoạt động cho admin với các biểu đồ trực quan, bao gồm:
- Thố<PERSON> kê số lượng người dùng, b<PERSON><PERSON> v<PERSON>, chủ đề phổ biến
- <PERSON><PERSON> tích hành vi người dùng như thời gian truy cập, từ khóa tìm kiếm
- Xu hướng tăng trưởng theo thời gian
- Biểu đồ tương tác với Recharts

## Các file đã tạo/chỉnh sửa

### Backend Models

1. **backend/models/UserActivity.js** - Mới
   - Lưu trữ hoạt động người dùng (login, view_post, search, etc.)
   - Thông tin session và device
   - Static methods để thống kê

2. **backend/models/SearchLog.js** - <PERSON>ớ<PERSON>
   - <PERSON><PERSON><PERSON> trữ lịch sử tìm kiếm
   - <PERSON><PERSON> khóa phổ biến và thất bại
   - <PERSON>h<PERSON><PERSON> kê theo thiết bị

### Backend Controllers & Routes

3. **backend/controllers/adminAnalyticsController.js** - Mới
   - `getOverviewStats()` - Thống kê tổng quan
   - `getUserActivityStats()` - Hoạt động người dùng
   - `getPopularContentStats()` - Nội dung phổ biến
   - `getSearchAnalytics()` - Phân tích tìm kiếm
   - `getGrowthTrends()` - Xu hướng tăng trưởng
   - `logUserActivity()` - Ghi log hoạt động
   - `logSearch()` - Ghi log tìm kiếm

4. **backend/routes/adminAnalyticsRoutes.js** - Mới
   - Routes cho tất cả API analytics
   - Phân quyền admin cho các endpoint thống kê

5. **backend/index.js** - Đã cập nhật
   - Thêm routes analytics

### Frontend

6. **frontend/src/pages/admin/AdminAnalyticsPage.jsx** - Mới
   - Trang thống kê với 4 tab chính
   - Biểu đồ Line, Bar, Pie, Area charts
   - Responsive design với Material-UI

7. **frontend/src/layouts/AdminDashboard.jsx** - Đã cập nhật
   - Thêm route analytics

8. **frontend/src/pages/admin/Sidebar.jsx** - Đã cập nhật
   - Thêm menu "Thống kê & Phân tích"

### Middleware & Utilities

9. **backend/middlewares/activityLogger.js** - Mới
   - Middleware tự động log hoạt động
   - Detect device, browser, OS
   - Log functions cho các hoạt động cụ thể

10. **backend/generate-sample-analytics-data.js** - Mới
    - Script tạo dữ liệu mẫu cho analytics
    - Tạo hoạt động người dùng và search logs

## API Endpoints

### Analytics (Admin only)
```
GET    /api/admin/analytics/overview           - Thống kê tổng quan
GET    /api/admin/analytics/user-activity      - Hoạt động người dùng
GET    /api/admin/analytics/popular-content    - Nội dung phổ biến
GET    /api/admin/analytics/search-analytics   - Phân tích tìm kiếm
GET    /api/admin/analytics/growth-trends      - Xu hướng tăng trưởng
```

### Logging (Public/User)
```
POST   /api/admin/analytics/log-activity       - Ghi log hoạt động
POST   /api/admin/analytics/log-search         - Ghi log tìm kiếm
```

## Các loại thống kê

### 1. Thống kê tổng quan
- Tổng số người dùng, bài viết, chủ đề, bình luận, lượt thích
- Số lượng mới trong khoảng thời gian
- Tỷ lệ tăng trưởng so với kỳ trước
- Người dùng hoạt động

### 2. Xu hướng tăng trưởng
- Biểu đồ line chart tăng trưởng theo thời gian
- Người dùng mới, bài viết mới, bình luận mới
- Tỷ lệ tăng trưởng phần trăm
- Hoạt động trong khoảng thời gian

### 3. Hoạt động người dùng
- Hoạt động theo giờ trong ngày (Bar chart)
- Phân loại hoạt động (Pie chart)
- Hoạt động theo ngày (Area chart)
- Top người dùng hoạt động nhất

### 4. Nội dung phổ biến
- Bài viết phổ biến nhất
- Chủ đề phổ biến nhất
- Thống kê theo danh mục (Bar chart)
- Engagement metrics

### 5. Phân tích tìm kiếm
- Từ khóa tìm kiếm phổ biến
- Xu hướng tìm kiếm theo thời gian
- Tìm kiếm thất bại
- Thống kê theo thiết bị
- Thời gian xử lý trung bình

## Cách sử dụng

### 1. Khởi động hệ thống
```bash
# Backend
cd backend
npm start

# Frontend
cd frontend
npm run dev
```

### 2. Tạo dữ liệu mẫu
```bash
cd backend
# Tạo tất cả dữ liệu mẫu
node generate-sample-analytics-data.js

# Chỉ tạo hoạt động người dùng
node generate-sample-analytics-data.js --activities-only

# Chỉ tạo search logs
node generate-sample-analytics-data.js --search-only

# Chỉ cập nhật view counts
node generate-sample-analytics-data.js --views-only
```

### 3. Truy cập trang analytics
- Đăng nhập với tài khoản admin
- Truy cập `/admin/analytics`
- Chọn khoảng thời gian (7 ngày, 30 ngày, 90 ngày, 1 năm)
- Xem các tab khác nhau

### 4. Tự động log hoạt động
```javascript
// Trong routes/controllers
const { logViewPost, executeActivityLog } = require('../middlewares/activityLogger');

// Áp dụng middleware
app.use(executeActivityLog);
router.get('/posts/:id', logViewPost, getPostById);
```

## Các loại hoạt động được log

1. **login** - Đăng nhập
2. **logout** - Đăng xuất
3. **view_post** - Xem bài viết
4. **create_post** - Tạo bài viết
5. **edit_post** - Sửa bài viết
6. **delete_post** - Xóa bài viết
7. **comment** - Bình luận
8. **like** - Thích
9. **search** - Tìm kiếm
10. **view_topic** - Xem chủ đề
11. **register** - Đăng ký
12. **profile_update** - Cập nhật profile
13. **page_view** - Xem trang

## Biểu đồ và Visualization

### Sử dụng Recharts
- **LineChart** - Xu hướng tăng trưởng, tìm kiếm theo thời gian
- **BarChart** - Hoạt động theo giờ, thống kê danh mục
- **PieChart** - Phân loại hoạt động, thống kê thiết bị
- **AreaChart** - Hoạt động theo ngày

### Tính năng tương tác
- Tooltip hiển thị chi tiết
- Legend có thể click
- Responsive design
- Color coding nhất quán

## Cấu hình và Tùy chỉnh

### Thời gian lưu trữ dữ liệu
```javascript
// Trong models, có thể thêm TTL index
schema.index({ timestamp: 1 }, { expireAfterSeconds: 60 * 60 * 24 * 90 }); // 90 ngày
```

### Thêm loại hoạt động mới
```javascript
// Trong UserActivity model
activityType: {
    enum: [...existingTypes, 'new_activity_type']
}
```

### Custom analytics
```javascript
// Tạo aggregation pipeline mới trong controller
const customStats = await UserActivity.aggregate([
    // Custom pipeline
]);
```

## Performance và Optimization

### Database Indexes
- Tất cả models đã có indexes tối ưu
- Compound indexes cho queries phức tạp
- Text indexes cho tìm kiếm

### Caching
- Có thể implement Redis caching cho queries thường xuyên
- Cache kết quả thống kê trong 5-15 phút

### Batch Processing
- Log hoạt động bất đồng bộ
- Bulk insert cho dữ liệu lớn

## Troubleshooting

### Lỗi thường gặp
1. **Không có dữ liệu hiển thị**: Chạy script tạo dữ liệu mẫu
2. **Biểu đồ không load**: Kiểm tra API responses và console errors
3. **Performance chậm**: Thêm indexes hoặc giảm khoảng thời gian query

### Debug
```bash
# Kiểm tra dữ liệu trong database
mongo
use your_database
db.useractivities.count()
db.searchlogs.count()
```

## Mở rộng tương lai

1. **Real-time analytics** với WebSocket
2. **Export reports** PDF/Excel
3. **Email reports** tự động
4. **Advanced filtering** và segmentation
5. **Predictive analytics** với ML
6. **A/B testing** analytics
7. **Heatmaps** và user journey tracking
