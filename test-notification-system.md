# Test Notification System

## ✅ Đã sửa các lỗi:

### 1. **Backend Controller Issues**
- ✅ Sửa AdminNotificationController để không phụ thuộc vào NotificationService
- ✅ Tạo notification trực tiếp bằng Notification model
- ✅ Thêm realtime notification qua Socket.IO
- ✅ Inject io instance qua middleware

### 2. **Socket.IO Integration**
- ✅ Gửi realtime notification đến user room: `user_${userId}`
- ✅ Emit event 'newNotification' với đầy đủ thông tin
- ✅ Populate sender và recipient information

### 3. **API Endpoints**
- ✅ `/api/admin/notifications/broadcast` - Gửi thông báo chung
- ✅ `/api/admin/notifications/individual` - Gửi thông báo cá nhân
- ✅ `/api/admin/notifications` - L<PERSON>y danh sách thông báo
- ✅ `/api/admin/notifications/stats` - Thống kê thông báo

## 🧪 Cách test hệ thống:

### **Test 1: <PERSON><PERSON><PERSON> thông báo chung (Broadcast)**
1. T<PERSON><PERSON> cập: `http://localhost:5173/admin/notifications`
2. Click "Gửi thông báo chung"
3. Điền form:
   - Tiêu đề: "Thông báo bảo trì hệ thống"
   - Nội dung: "Hệ thống sẽ bảo trì từ 2h-4h sáng ngày mai"
   - Loại: "Bảo trì hệ thống"
   - Độ ưu tiên: "Cao"
   - Đối tượng: "Tất cả"
4. Click "Gửi thông báo"
5. ✅ Kiểm tra: Thông báo thành công, danh sách cập nhật

### **Test 2: Gửi thông báo cá nhân**
1. Click "Gửi thông báo cá nhân"
2. Điền form:
   - Tiêu đề: "Chúc mừng bài viết của bạn"
   - Nội dung: "Bài viết của bạn đã được nhiều người quan tâm"
   - Người nhận: Chọn user từ dropdown
   - Độ ưu tiên: "Bình thường"
3. Click "Gửi thông báo"
4. ✅ Kiểm tra: Thông báo thành công

### **Test 3: Realtime Notification**
1. Mở 2 browser/tab:
   - Tab 1: Admin panel (gửi thông báo)
   - Tab 2: User đăng nhập (nhận thông báo)
2. Từ admin gửi thông báo cá nhân cho user
3. ✅ Kiểm tra: User nhận thông báo realtime (bell icon, popup)

### **Test 4: Thống kê thông báo**
1. Xem các card thống kê:
   - Tổng thông báo
   - Chưa đọc
   - Đã đọc
   - Hôm nay
2. ✅ Kiểm tra: Số liệu cập nhật đúng

## 🔧 Debug nếu có lỗi:

### **Backend Logs để kiểm tra:**
```bash
# Trong terminal backend
📢 Realtime notification sent to user: [userId]
✅ Message sent to receiver successfully
👤 User [userId] connected with socket [socketId]
```

### **Frontend Console để kiểm tra:**
```javascript
// Mở Developer Tools > Console
// Kiểm tra API calls
Network tab > XHR > admin/notifications/broadcast
Response: {"success": true, "message": "Đã gửi thông báo đến X người dùng"}
```

### **Socket Events để kiểm tra:**
```javascript
// Trong console frontend
socket.on('newNotification', (data) => {
    console.log('📨 Received notification:', data);
});
```

## 🎯 Expected Results:

### **Gửi thông báo chung:**
- ✅ Gửi đến tất cả users có role phù hợp
- ✅ Hiển thị snackbar thành công
- ✅ Cập nhật danh sách thông báo
- ✅ Cập nhật thống kê

### **Gửi thông báo cá nhân:**
- ✅ Gửi đến user được chọn
- ✅ Realtime notification qua Socket.IO
- ✅ Hiển thị trong notification bell của user
- ✅ Email notification (nếu cấu hình)

### **UI/UX:**
- ✅ Form validation (title, message required)
- ✅ Loading states khi gửi
- ✅ Success/error messages
- ✅ Dialog đóng sau khi gửi thành công
- ✅ Form reset sau khi gửi

## 🚀 Tính năng hoàn chỉnh:

1. **Admin có thể:**
   - Gửi thông báo chung cho tất cả/user/admin
   - Gửi thông báo cá nhân cho user cụ thể
   - Xem danh sách thông báo đã gửi
   - Xem thống kê thông báo
   - Xóa thông báo
   - Đặt độ ưu tiên và thời gian hết hạn

2. **Users sẽ nhận:**
   - Thông báo realtime qua Socket.IO
   - Notification bell với số lượng chưa đọc
   - Email notification cho các thông báo quan trọng
   - Action URL để navigate đến trang liên quan

3. **System features:**
   - Realtime delivery
   - Persistent storage
   - Priority levels
   - Expiration dates
   - Metadata tracking
   - Read/unread status

Hệ thống notification đã hoàn thiện và sẵn sàng sử dụng! 🎉
