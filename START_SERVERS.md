# 🚀 Start Backend & Frontend Servers

## 🔧 **Connection Error Fix**

Lỗi `ERR_CONNECTION_REFUSED` xảy ra vì:
- **Backend không chạy** hoặc chạy sai port
- **CORS configuration** không match với frontend port
- **Socket.IO connection** bị block

## ✅ **Solution Applied:**

### **1. Fixed CORS Configuration**
```javascript
// ✅ Backend index.js - Support cả 2 ports
const io = new Server(server, {
    cors: {
        origin: ["http://localhost:5173", "http://localhost:5174"],
        methods: ["GET", "POST", "PUT", "DELETE"]
    }
});

app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:5174'],
    credentials: true,
}));
```

### **2. Start Backend Server**
```bash
# Terminal 1 - Backend
cd backend
npm start
# hoặc
node index.js

# Should see:
# Server running on port 5000
# Database connected successfully
```

### **3. Start Frontend Server**
```bash
# Terminal 2 - Frontend  
cd frontend
npm start
# hoặc
npm run dev

# Should see:
# Local: http://localhost:5174
# Network: http://192.168.x.x:5174
```

## 🔍 **Troubleshooting Steps:**

### **Step 1: Check Backend Status**
```bash
cd backend
npm start
```

**Expected Output:**
```
Server running on port 5000
Database connected successfully
```

### **Step 2: Check Frontend Status**
```bash
cd frontend
npm run dev
```

**Expected Output:**
```
VITE v4.x.x ready in xxx ms
➜ Local:   http://localhost:5174/
➜ Network: http://192.168.x.x:5174/
```

### **Step 3: Test API Connection**
Open browser và check:
```
http://localhost:5000/api/topics
```

Should return JSON data, not connection error.

### **Step 4: Check Console Errors**
In browser console, should see:
```
✅ Socket connected
✅ API calls successful
❌ No ERR_CONNECTION_REFUSED errors
```

## 📋 **Port Configuration:**

### **Backend (Express + Socket.IO)**
- **Port**: 5000
- **API Base**: http://localhost:5000/api
- **Socket**: http://localhost:5000

### **Frontend (Vite/React)**
- **Port**: 5174 (or 5173)
- **Dev Server**: http://localhost:5174
- **API Calls**: → http://localhost:5000/api

### **CORS Origins**
```javascript
// ✅ Now supports both ports
origin: ['http://localhost:5173', 'http://localhost:5174']
```

## 🚀 **Quick Start Commands:**

### **Option 1: Manual Start**
```bash
# Terminal 1
cd backend
npm start

# Terminal 2  
cd frontend
npm run dev
```

### **Option 2: Package.json Scripts**
```json
// backend/package.json
{
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js"
  }
}

// frontend/package.json
{
  "scripts": {
    "dev": "vite",
    "start": "vite"
  }
}
```

## 🔧 **Environment Variables:**

### **Backend (.env)**
```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/your-database
JWT_SECRET=your-jwt-secret
```

### **Frontend (if needed)**
```env
VITE_API_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000
```

## 📱 **Test Connection:**

### **1. API Test**
```bash
curl http://localhost:5000/api/topics
```

### **2. Frontend Test**
```
http://localhost:5174/
```

### **3. Socket Test**
Check browser console for:
```
Socket connected: xxx
User connected: xxx
```

## ❌ **Common Issues & Fixes:**

### **Issue 1: Backend not starting**
```bash
# Check if port 5000 is in use
netstat -an | findstr :5000

# Kill process if needed
taskkill /F /PID <process-id>
```

### **Issue 2: Frontend can't connect**
```javascript
// Check API configuration
// frontend/src/services/api.jsx
baseURL: 'http://localhost:5000/api'  // ✅ Correct

// Check socket configuration  
// frontend/src/socket.jsx
const SOCKET_SERVER_URL = 'http://localhost:5000';  // ✅ Correct
```

### **Issue 3: CORS errors**
```javascript
// Backend should have:
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:5174'],
    credentials: true,
}));
```

### **Issue 4: Database connection**
```bash
# Make sure MongoDB is running
mongod
# or
brew services start mongodb-community
```

## 🎯 **Success Indicators:**

### **✅ Backend Running**
- Console shows: "Server running on port 5000"
- API endpoints respond: http://localhost:5000/api/topics
- Socket.IO ready: "Socket.IO server initialized"

### **✅ Frontend Running**  
- Vite dev server: http://localhost:5174
- No console errors
- API calls successful
- Socket connected

### **✅ Full Stack Working**
- Breadcrumb navigation shows correctly
- PostDetail loads data
- Real-time features work
- No ERR_CONNECTION_REFUSED errors

---

**🚀 Start both servers và test breadcrumb navigation!**

**Backend**: http://localhost:5000
**Frontend**: http://localhost:5174
**PostDetail**: http://localhost:5174/post-detail?topicId=123&postId=456

**Connection errors should be resolved!** 🔧✨
