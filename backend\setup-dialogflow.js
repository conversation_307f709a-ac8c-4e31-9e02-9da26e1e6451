// File: backend/setup-dialogflow.js
// Script hướng dẫn setup Dialogflow

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setupDialogflow() {
    console.log('🤖 Dialogflow Setup Wizard\n');
    console.log('Hướng dẫn thiết lập Dialogflow cho hệ thống chatbot\n');
    
    console.log('📋 <PERSON><PERSON><PERSON> bước cần thực hiện:');
    console.log('1. Tạo Google Cloud Project');
    console.log('2. Enable Dialogflow API');
    console.log('3. Tạo Service Account');
    console.log('4. Download JSON key file');
    console.log('5. Cấu hình environment variables\n');
    
    const proceed = await question('Bạn có muốn tiếp tục? (y/n): ');
    if (proceed.toLowerCase() !== 'y') {
        console.log('Setup bị hủy.');
        rl.close();
        return;
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('BƯỚC 1: Tạo Google Cloud Project');
    console.log('='.repeat(60));
    console.log('1. Truy cập: https://console.cloud.google.com/');
    console.log('2. Click "Select a project" → "New Project"');
    console.log('3. Nhập tên project (ví dụ: "my-chatbot-project")');
    console.log('4. Click "Create"');
    console.log('5. Chọn project vừa tạo');
    
    await question('\nNhấn Enter khi đã hoàn thành bước 1...');
    
    console.log('\n' + '='.repeat(60));
    console.log('BƯỚC 2: Enable Dialogflow API');
    console.log('='.repeat(60));
    console.log('1. Trong Google Cloud Console, vào "APIs & Services" → "Library"');
    console.log('2. Tìm kiếm "Dialogflow API"');
    console.log('3. Click vào "Dialogflow API" và click "Enable"');
    console.log('4. Đợi API được enable');
    
    await question('\nNhấn Enter khi đã hoàn thành bước 2...');
    
    console.log('\n' + '='.repeat(60));
    console.log('BƯỚC 3: Tạo Service Account');
    console.log('='.repeat(60));
    console.log('1. Vào "IAM & Admin" → "Service Accounts"');
    console.log('2. Click "Create Service Account"');
    console.log('3. Nhập tên (ví dụ: "dialogflow-service")');
    console.log('4. Click "Create and Continue"');
    console.log('5. Chọn role "Dialogflow API Admin"');
    console.log('6. Click "Continue" → "Done"');
    
    await question('\nNhấn Enter khi đã hoàn thành bước 3...');
    
    console.log('\n' + '='.repeat(60));
    console.log('BƯỚC 4: Download JSON Key');
    console.log('='.repeat(60));
    console.log('1. Click vào service account vừa tạo');
    console.log('2. Vào tab "Keys"');
    console.log('3. Click "Add Key" → "Create new key"');
    console.log('4. Chọn "JSON" và click "Create"');
    console.log('5. File JSON sẽ được download tự động');
    console.log('6. Lưu file này vào thư mục backend/ (ví dụ: backend/dialogflow-key.json)');
    
    await question('\nNhấn Enter khi đã hoàn thành bước 4...');
    
    console.log('\n' + '='.repeat(60));
    console.log('BƯỚC 5: Cấu hình Environment Variables');
    console.log('='.repeat(60));
    
    const projectId = await question('Nhập Project ID từ Google Cloud Console: ');
    const keyFilePath = await question('Nhập đường dẫn đến file JSON key (ví dụ: ./dialogflow-key.json): ');
    
    // Tạo hoặc cập nhật file .env
    const envPath = path.join(__dirname, '.env');
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
        envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Cập nhật hoặc thêm Dialogflow config
    const dialogflowConfig = `
# Dialogflow Configuration
DIALOGFLOW_PROJECT_ID=${projectId}
GOOGLE_APPLICATION_CREDENTIALS=${keyFilePath}
DIALOGFLOW_LANGUAGE_CODE=vi
`;
    
    // Kiểm tra xem đã có config Dialogflow chưa
    if (envContent.includes('DIALOGFLOW_PROJECT_ID')) {
        console.log('\n⚠️  File .env đã có cấu hình Dialogflow');
        const overwrite = await question('Bạn có muốn ghi đè? (y/n): ');
        if (overwrite.toLowerCase() === 'y') {
            // Remove existing Dialogflow config
            envContent = envContent.replace(/# Dialogflow Configuration[\s\S]*?DIALOGFLOW_LANGUAGE_CODE=.*\n/g, '');
            envContent += dialogflowConfig;
            fs.writeFileSync(envPath, envContent);
            console.log('✅ Đã cập nhật file .env');
        }
    } else {
        envContent += dialogflowConfig;
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Đã thêm cấu hình Dialogflow vào file .env');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('BƯỚC 6: Kiểm tra cấu hình');
    console.log('='.repeat(60));
    
    const testConfig = await question('Bạn có muốn test cấu hình ngay bây giờ? (y/n): ');
    if (testConfig.toLowerCase() === 'y') {
        console.log('\nĐang kiểm tra cấu hình...');
        try {
            // Reload environment variables
            delete require.cache[require.resolve('dotenv')];
            require('dotenv').config();
            
            const { checkDialogflowConfig } = require('./check-dialogflow-config');
            const configOK = checkDialogflowConfig();
            
            if (configOK) {
                console.log('\n🎉 Setup hoàn tất! Bạn có thể sử dụng chatbot ngay bây giờ.');
                
                const createSample = await question('\nBạn có muốn tạo dữ liệu mẫu? (y/n): ');
                if (createSample.toLowerCase() === 'y') {
                    console.log('\nĐang tạo dữ liệu mẫu...');
                    try {
                        const { generateSampleIntents } = require('./generate-sample-chatbot-data');
                        await generateSampleIntents();
                        console.log('✅ Đã tạo dữ liệu mẫu thành công!');
                    } catch (error) {
                        console.log('❌ Lỗi khi tạo dữ liệu mẫu:', error.message);
                    }
                }
            }
        } catch (error) {
            console.log('❌ Lỗi khi kiểm tra cấu hình:', error.message);
        }
    }
    
    console.log('\n📚 Tài liệu tham khảo:');
    console.log('- Google Cloud Console: https://console.cloud.google.com/');
    console.log('- Dialogflow Documentation: https://cloud.google.com/dialogflow/docs');
    console.log('- Project README: ./ADMIN_CHATBOT_MANAGEMENT_README.md');
    
    console.log('\n🚀 Các lệnh hữu ích:');
    console.log('- Kiểm tra cấu hình: node check-dialogflow-config.js');
    console.log('- Test kết nối: node check-dialogflow-config.js --test-connection');
    console.log('- Tạo dữ liệu mẫu: node generate-sample-chatbot-data.js');
    console.log('- Test APIs: node test-chatbot.js');
    
    rl.close();
}

// Chạy setup
if (require.main === module) {
    setupDialogflow().catch(error => {
        console.error('❌ Lỗi setup:', error);
        rl.close();
        process.exit(1);
    });
}

module.exports = { setupDialogflow };
