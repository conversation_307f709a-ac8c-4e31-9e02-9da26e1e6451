const mongoose = require('mongoose');
const Comment = require('./models/Comment');
const Post = require('./models/Post');
const User = require('./models/User');
const CommentLike = require('./models/CommentLike');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/your_database_name', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
});

async function testNestedComments() {
    try {
        console.log('🧪 Testing Nested Comments System...\n');

        // Clean up existing test data
        await Comment.deleteMany({ content: { $regex: /Test Comment|Test Reply/ } });
        await CommentLike.deleteMany({});

        // Find or create a test user
        let testUser = await User.findOne({ username: 'testuser' });
        if (!testUser) {
            try {
                testUser = new User({
                    username: 'testuser',
                    email: '<EMAIL>',
                    fullName: 'Test User',
                    password: 'hashedpassword'
                });
                await testUser.save();
                console.log('✅ Created test user');
            } catch (error) {
                if (error.code === 11000) {
                    // User already exists, find it
                    testUser = await User.findOne({ email: '<EMAIL>' });
                    console.log('✅ Found existing test user');
                } else {
                    throw error;
                }
            }
        } else {
            console.log('✅ Found existing test user');
        }

        // Find or create a test post
        let testPost = await Post.findOne({ title: { $regex: /Test Post/ } });
        if (!testPost) {
            testPost = new Post({
                title: 'Test Post for Comments',
                content: 'This is a test post for testing nested comments',
                authorId: testUser._id,
                topicId: new mongoose.Types.ObjectId() // Dummy topic ID
            });
            await testPost.save();
            console.log('✅ Created test post');
        }

        // Test 1: Create root comment
        console.log('\n📝 Test 1: Creating root comment...');
        const rootComment = new Comment({
            postId: testPost._id,
            authorId: testUser._id,
            content: 'Test Comment - Root Level',
            level: 0
        });
        await rootComment.save();
        console.log('✅ Root comment created:', rootComment._id);

        // Test 2: Create first level reply
        console.log('\n📝 Test 2: Creating first level reply...');
        const firstReply = new Comment({
            postId: testPost._id,
            authorId: testUser._id,
            content: 'Test Reply - Level 1',
            parentCommentId: rootComment._id,
            level: 1,
            rootCommentId: rootComment._id
        });
        await firstReply.save();

        // Update parent reply count
        await Comment.findByIdAndUpdate(rootComment._id, { $inc: { replyCount: 1 } });
        console.log('✅ First level reply created:', firstReply._id);

        // Test 3: Create second level reply (nested)
        console.log('\n📝 Test 3: Creating second level reply...');
        const secondReply = new Comment({
            postId: testPost._id,
            authorId: testUser._id,
            content: 'Test Reply - Level 2 (nested)',
            parentCommentId: firstReply._id,
            level: 2,
            rootCommentId: rootComment._id
        });
        await secondReply.save();

        // Update parent reply count
        await Comment.findByIdAndUpdate(firstReply._id, { $inc: { replyCount: 1 } });
        console.log('✅ Second level reply created:', secondReply._id);

        // Test 4: Create third level reply (deeply nested)
        console.log('\n📝 Test 4: Creating third level reply...');
        const thirdReply = new Comment({
            postId: testPost._id,
            authorId: testUser._id,
            content: 'Test Reply - Level 3 (deeply nested)',
            parentCommentId: secondReply._id,
            level: 3,
            rootCommentId: rootComment._id
        });
        await thirdReply.save();

        // Update parent reply count
        await Comment.findByIdAndUpdate(secondReply._id, { $inc: { replyCount: 1 } });
        console.log('✅ Third level reply created:', thirdReply._id);

        // Test 5: Test comment likes
        console.log('\n❤️ Test 5: Testing comment likes...');

        // Like root comment
        await CommentLike.create({ commentId: rootComment._id, userId: testUser._id });
        await Comment.findByIdAndUpdate(rootComment._id, { $inc: { likeCount: 1 } });

        // Like nested reply
        await CommentLike.create({ commentId: thirdReply._id, userId: testUser._id });
        await Comment.findByIdAndUpdate(thirdReply._id, { $inc: { likeCount: 1 } });

        console.log('✅ Comment likes added');

        // Test 6: Retrieve nested comments structure
        console.log('\n🔍 Test 6: Retrieving nested comments structure...');

        // Get root comments
        const rootComments = await Comment.find({
            postId: testPost._id,
            parentCommentId: null
        }).populate('authorId', 'username fullName').lean();

        // Get all replies for this post
        const allReplies = await Comment.find({
            postId: testPost._id,
            parentCommentId: { $ne: null }
        }).populate('authorId', 'username fullName').lean();

        console.log('📊 Comments structure:');
        console.log(`- Root comments: ${rootComments.length}`);
        console.log(`- Total replies: ${allReplies.length}`);

        // Build nested structure
        const commentMap = new Map();

        // Add root comments
        rootComments.forEach(comment => {
            commentMap.set(comment._id.toString(), { ...comment, replies: [] });
        });

        // Add replies
        allReplies.forEach(reply => {
            commentMap.set(reply._id.toString(), { ...reply, replies: [] });
        });

        // Build tree
        allReplies.forEach(reply => {
            const parentId = reply.parentCommentId.toString();
            if (commentMap.has(parentId)) {
                commentMap.get(parentId).replies.push(commentMap.get(reply._id.toString()));
            }
        });

        // Display structure
        function displayCommentTree(comment, indent = '') {
            console.log(`${indent}├─ [Level ${comment.level}] ${comment.content} (Likes: ${comment.likeCount || 0})`);
            if (comment.replies && comment.replies.length > 0) {
                comment.replies.forEach(reply => {
                    displayCommentTree(reply, indent + '  ');
                });
            }
        }

        rootComments.forEach(comment => {
            const fullComment = commentMap.get(comment._id.toString());
            displayCommentTree(fullComment);
        });

        // Test 7: Test deletion of nested comments
        console.log('\n🗑️ Test 7: Testing deletion of nested comments...');

        // Find all comments that should be deleted when deleting the first reply
        const commentsToDelete = await Comment.find({
            $or: [
                { _id: firstReply._id },
                { rootCommentId: firstReply._id },
                { parentCommentId: firstReply._id }
            ]
        });

        console.log(`Found ${commentsToDelete.length} comments to delete when deleting first reply`);

        // Delete associated likes
        const commentIdsToDelete = commentsToDelete.map(c => c._id);
        await CommentLike.deleteMany({ commentId: { $in: commentIdsToDelete } });

        // Delete comments
        await Comment.deleteMany({ _id: { $in: commentIdsToDelete } });

        // Update parent reply count
        await Comment.findByIdAndUpdate(rootComment._id, { $inc: { replyCount: -1 } });

        console.log('✅ Nested comments deleted successfully');

        // Verify final state
        const remainingComments = await Comment.find({ postId: testPost._id });
        console.log(`\n📈 Final state: ${remainingComments.length} comments remaining`);

        remainingComments.forEach(comment => {
            console.log(`- [Level ${comment.level}] ${comment.content}`);
        });

        console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        mongoose.connection.close();
    }
}

// Run tests
testNestedComments();
