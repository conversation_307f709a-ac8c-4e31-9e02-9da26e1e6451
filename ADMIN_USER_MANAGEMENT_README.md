# H<PERSON> thống Quản lý Người dùng cho Admin

## Tổng quan
Hệ thống quản lý người dùng cho admin cho phép quản trị viên:
- Xem danh sách và thông tin chi tiết người dùng
- C<PERSON><PERSON> báo người dùng vi phạm
- Tạm khóa hoặc cấm tài khoản
- Thay đổi vai trò người dùng
- Xem thống kê người dùng

## Các file đã tạo/chỉnh sửa

### Backend

1. **backend/models/User.js** - <PERSON><PERSON> cập nhật
   - Thêm các trường: `status`, `warnings`, `suspensionInfo`, `banInfo`, `lastLogin`, `loginAttempts`

2. **backend/controllers/adminUserController.js** - Mới
   - `getAllUsers()` - <PERSON><PERSON><PERSON> danh sách người dùng với phân trang và tìm kiếm
   - `getUserById()` - <PERSON><PERSON><PERSON> thông tin chi tiết người dùng
   - `warnUser()` - <PERSON><PERSON><PERSON> báo người dùng
   - `suspendUser()` - Tạm khóa tài khoản
   - `banUser()` - Cấm tài khoản vĩnh viễn
   - `activateUser()` - Kích hoạt lại tài khoản
   - `updateUserRole()` - Thay đổi vai trò
   - `removeWarning()` - Xóa cảnh báo
   - `getUserStats()` - Lấy thống kê người dùng

3. **backend/routes/adminUserRoutes.js** - Mới
   - Định nghĩa các routes cho quản lý người dùng
   - Áp dụng middleware xác thực và phân quyền admin

4. **backend/index.js** - Đã cập nhật
   - Thêm import và route cho adminUserRoutes

5. **backend/controllers/authController.js** - Đã cập nhật
   - Kiểm tra trạng thái tài khoản khi đăng nhập
   - Cập nhật lastLogin và loginAttempts

6. **backend/middlewares/authMiddleware.js** - Đã cập nhật
   - Kiểm tra trạng thái tài khoản trong middleware

### Frontend

7. **frontend/src/pages/admin/AdminUsersPage.jsx** - Đã cập nhật hoàn toàn
   - Giao diện quản lý người dùng với bảng, tìm kiếm, lọc
   - Dialog xem chi tiết người dùng
   - Dialog thực hiện các hành động (cảnh báo, khóa, cấm, etc.)
   - Thống kê người dùng

### Test

8. **backend/test-admin-users.js** - Mới
   - Script test các API quản lý người dùng

## API Endpoints

### Quản lý người dùng (Admin only)

```
GET    /api/admin/users/stats           - Lấy thống kê người dùng
GET    /api/admin/users                 - Lấy danh sách người dùng
GET    /api/admin/users/:id             - Lấy thông tin chi tiết người dùng
POST   /api/admin/users/:id/warn        - Cảnh báo người dùng
PUT    /api/admin/users/:id/suspend     - Tạm khóa tài khoản
PUT    /api/admin/users/:id/ban         - Cấm tài khoản
PUT    /api/admin/users/:id/activate    - Kích hoạt tài khoản
PUT    /api/admin/users/:id/role        - Thay đổi vai trò
DELETE /api/admin/users/:id/warnings/:warningId - Xóa cảnh báo
```

## Cách sử dụng

### 1. Khởi động server
```bash
cd backend
npm start
```

### 2. Khởi động frontend
```bash
cd frontend
npm run dev
```

### 3. Truy cập trang admin
- Đăng nhập với tài khoản admin
- Truy cập `/admin/users` để quản lý người dùng

### 4. Các chức năng chính

#### Xem danh sách người dùng
- Hiển thị bảng với thông tin cơ bản
- Tìm kiếm theo tên hoặc email
- Lọc theo vai trò và trạng thái
- Phân trang

#### Xem chi tiết người dùng
- Click vào icon "Xem" để mở dialog chi tiết
- Hiển thị thông tin đầy đủ, cảnh báo, thông tin khóa/cấm

#### Cảnh báo người dùng
- Click vào icon "Cảnh báo"
- Nhập lý do và nội dung cảnh báo
- Cảnh báo sẽ được lưu vào lịch sử

#### Tạm khóa tài khoản
- Click vào icon "Khóa"
- Nhập lý do và thời gian khóa (tùy chọn)
- Người dùng sẽ không thể đăng nhập

#### Cấm tài khoản
- Click vào icon "Cấm"
- Nhập lý do cấm
- Tài khoản sẽ bị cấm vĩnh viễn

#### Kích hoạt tài khoản
- Click vào icon "Kích hoạt" cho tài khoản bị khóa/cấm
- Tài khoản sẽ được kích hoạt lại

#### Thay đổi vai trò
- Click vào icon "Chỉnh sửa"
- Chọn vai trò mới (user, editor, admin)

## Bảo mật

- Tất cả API đều yêu cầu xác thực admin
- Không thể thực hiện hành động trên admin khác
- Kiểm tra trạng thái tài khoản khi đăng nhập và trong middleware
- Lưu lịch sử các hành động với thông tin admin thực hiện

## Lưu ý

1. Đảm bảo có ít nhất một tài khoản admin để quản lý
2. Cẩn thận khi cấm tài khoản vì đây là hành động nghiêm trọng
3. Thường xuyên kiểm tra và xóa các cảnh báo không cần thiết
4. Backup database trước khi thực hiện các thay đổi lớn

## Test

Để test các API, sử dụng file `backend/test-admin-users.js`:

```bash
cd backend
node test-admin-users.js
```

Nhớ cập nhật token admin và user ID trong file test trước khi chạy.
