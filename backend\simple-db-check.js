// Simple database check
require('dotenv').config();
const { MongoClient } = require('mongodb');

const checkDatabases = async () => {
    console.log('🔍 Checking MongoDB Databases...\n');
    
    console.log('📋 Configuration:');
    console.log(`   MONGO_URI: ${process.env.MONGO_URI}`);
    
    const baseUri = process.env.MONGO_URI.replace(/\/[^\/]*$/, '');
    console.log(`   Base URI: ${baseUri}`);
    
    try {
        const client = new MongoClient(baseUri);
        await client.connect();
        console.log('✅ Connected to MongoDB\n');
        
        const adminDb = client.db().admin();
        const databases = await adminDb.listDatabases();
        
        console.log('📊 Available Databases:');
        let foundDienDan = false;
        let foundHilu = false;
        
        for (const db of databases.databases) {
            const sizeMB = (db.sizeOnDisk / 1024 / 1024).toFixed(2);
            console.log(`   - ${db.name} (${sizeMB} MB)`);
            
            if (db.name === 'dien_dan_TVU') {
                foundDienDan = true;
                
                // Check collections in dien_dan_TVU
                const dienDanDB = client.db('dien_dan_TVU');
                const collections = await dienDanDB.listCollections().toArray();
                console.log(`     Collections: ${collections.length}`);
                
                // Count key documents
                try {
                    const userCount = await dienDanDB.collection('users').countDocuments();
                    const postCount = await dienDanDB.collection('posts').countDocuments();
                    const topicCount = await dienDanDB.collection('topics').countDocuments();
                    console.log(`     Users: ${userCount}, Posts: ${postCount}, Topics: ${topicCount}`);
                } catch (e) {
                    console.log(`     Error counting documents: ${e.message}`);
                }
            }
            
            if (db.name === 'hilu-auau') {
                foundHilu = true;
                
                // Check collections in hilu-auau
                const hiluDB = client.db('hilu-auau');
                const collections = await hiluDB.listCollections().toArray();
                console.log(`     Collections: ${collections.length}`);
                
                // Count key documents
                try {
                    const userCount = await hiluDB.collection('users').countDocuments();
                    const postCount = await hiluDB.collection('posts').countDocuments();
                    const topicCount = await hiluDB.collection('topics').countDocuments();
                    console.log(`     Users: ${userCount}, Posts: ${postCount}, Topics: ${topicCount}`);
                } catch (e) {
                    console.log(`     Error counting documents: ${e.message}`);
                }
            }
        }
        
        console.log('\n🎯 Analysis:');
        console.log(`   ✅ dien_dan_TVU exists: ${foundDienDan ? 'YES' : 'NO'}`);
        console.log(`   ⚠️  hilu-auau exists: ${foundHilu ? 'YES' : 'NO'}`);
        
        if (foundHilu && foundDienDan) {
            console.log('\n💡 Recommendation:');
            console.log('   - You have both databases');
            console.log('   - Current .env points to: dien_dan_TVU ✅');
            console.log('   - Consider dropping hilu-auau if not needed');
            console.log('   - Or migrate data from hilu-auau to dien_dan_TVU');
        } else if (foundDienDan) {
            console.log('\n✅ Perfect! Using dien_dan_TVU as configured');
        } else if (foundHilu) {
            console.log('\n⚠️  Only hilu-auau exists, but .env points to dien_dan_TVU');
            console.log('   - Either update .env to use hilu-auau');
            console.log('   - Or rename hilu-auau to dien_dan_TVU');
        }
        
        await client.close();
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
};

checkDatabases().then(() => {
    console.log('\n✨ Check completed!');
    process.exit(0);
}).catch(console.error);
