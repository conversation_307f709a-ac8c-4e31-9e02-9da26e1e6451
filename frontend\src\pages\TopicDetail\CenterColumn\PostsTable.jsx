import React from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Typography,
    Box,
    Tooltip,
    Avatar,
    Chip,
    useTheme,
    alpha
} from '@mui/material';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import { Comment, ThumbUp, Star } from '@mui/icons-material';
import { useNavigate, Link } from 'react-router-dom';

// Helper function to construct full URL for images
const constructAvatarUrl = (url) => {
    if (!url) return null;
    if (url.startsWith('/upload')) {
        return `http://localhost:5000${url}`;
    }
    return url;
};

const PostsTable = ({ posts, topicId }) => {
    const navigate = useNavigate();
    const theme = useTheme();
    const darkMode = theme.palette.mode === 'dark';

    const handleRowClick = (postId) => {
        navigate(`/posts/detail?topicId=${topicId}&postId=${postId}`);
    };

    const handleAuthorClick = (e) => {
        e.stopPropagation(); // Prevent row click when clicking on the author link
    };

    return (
        <TableContainer component={Paper} sx={{
            boxShadow: 'none',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: 2,
            background: darkMode ? '#242526' : '#ffffff',
        }}>
            <Table aria-label="posts table">
                <TableHead sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
                    <TableRow>
                        <TableCell sx={{ fontWeight: 'bold', color: darkMode ? '#e4e6eb' : '#1c1e21' }}>Tiêu đề</TableCell>
                        <TableCell align="center" sx={{ fontWeight: 'bold', color: darkMode ? '#e4e6eb' : '#1c1e21' }}>Thống kê</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', color: darkMode ? '#e4e6eb' : '#1c1e21' }}>Tác giả</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {posts.map((post) => (
                        <TableRow
                            key={post._id}
                            hover
                            onClick={() => handleRowClick(post._id)}
                            sx={{
                                cursor: 'pointer',
                                '&:last-child td, &:last-child th': { border: 0 },
                                '&:hover': {
                                    backgroundColor: alpha(theme.palette.primary.main, 0.05)
                                }
                            }}
                        >
                            <TableCell component="th" scope="row">
                                <Typography variant="body1" fontWeight="medium" color={darkMode ? '#e4e6eb' : '#1c1e21'}>{post.title}</Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                                    <Tooltip title="Bình luận">
                                        <Chip icon={<Comment />} label={post.commentCount || 0} size="small" variant="outlined" />
                                    </Tooltip>
                                    <Tooltip title="Lượt thích">
                                        <Chip icon={<ThumbUp />} label={post.likesCount || 0} size="small" variant="outlined" />
                                    </Tooltip>
                                    <Tooltip title="Đánh giá trung bình">
                                        <Chip icon={<Star />} label={post.averageRating ? post.averageRating.toFixed(1) : 'N/A'} size="small" variant="outlined" />
                                    </Tooltip>
                                </Box>
                            </TableCell>
                            <TableCell>
                                {post.author ? (
                                    <Link to={`/profile/${post.author._id}`} onClick={handleAuthorClick} style={{ textDecoration: 'none', color: 'inherit' }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', '&:hover .author-name': { textDecoration: 'underline' } }}>
                                            <Avatar src={constructAvatarUrl(post.author.avatarUrl)} sx={{ width: 32, height: 32, mr: 1.5 }} />
                                            <Box>
                                                <Typography className="author-name" variant="body2" fontWeight="bold" color={darkMode ? '#e4e6eb' : '#1c1e21'}>
                                                    {post.author.username || post.author.fullName}
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    {formatDistanceToNow(new Date(post.createdAt), { addSuffix: true, locale: vi })}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </Link>
                                ) : (
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Avatar sx={{ width: 32, height: 32, mr: 1.5 }} />
                                        <Box>
                                            <Typography variant="body2" fontWeight="bold">Người dùng ẩn danh</Typography>
                                            <Typography variant="caption" color="text.secondary">
                                                {formatDistanceToNow(new Date(post.createdAt), { addSuffix: true, locale: vi })}
                                            </Typography>
                                        </Box>
                                    </Box>
                                )}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );
};

export default PostsTable;
