// Test image upload and processing
require('dotenv').config();
const axios = require('axios');

const testImageUpload = async () => {
    console.log('🧪 Testing image upload and processing...\n');

    // Test data with base64 image
    const testContent = `
        <h2>Test Post with Image</h2>
        <p>This is a test post with an embedded image:</p>
        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==" alt="Test Image" />
        <p>End of test content.</p>
    `;

    const testPost = {
        title: 'Test Post with Image Upload',
        content: testContent,
        topicId: '67444f178b0ce2263d27dd0e', // Use existing topic ID
        tags: ['test', 'image'],
        authorId: '674450178b0ce2263d27dd1a' // Use existing user ID
    };

    try {
        console.log('📝 Sending test post...');
        console.log('Content preview:', testContent.substring(0, 200));
        console.log('Has data: URL:', testContent.includes('data:'));

        // Test without auth first - call createPost directly
        const postController = require('./controllers/postController');
        const mongoose = require('mongoose');

        // Connect to DB
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to MongoDB');

        // Mock request and response objects
        const req = {
            body: testPost
        };

        const res = {
            status: (code) => ({
                json: (data) => {
                    console.log(`\n✅ Response status: ${code}`);
                    console.log('Response data:', JSON.stringify(data, null, 2));

                    if (data.post) {
                        console.log('\n📄 Saved post content preview:');
                        console.log(data.post.content.substring(0, 500));
                        console.log('\nHas server URLs:', data.post.content.includes('http://localhost:5000/upload/'));
                    }
                }
            }),
            json: (data) => {
                console.log('\n✅ Success response:');
                console.log('Response data:', JSON.stringify(data, null, 2));

                if (data.post) {
                    console.log('\n📄 Saved post content preview:');
                    console.log(data.post.content.substring(0, 500));
                    console.log('\nHas server URLs:', data.post.content.includes('http://localhost:5000/upload/'));
                }
            }
        };

        // Call createPost directly
        await postController.createPost(req, res);

        mongoose.connection.close();

    } catch (error) {
        console.error('❌ Error creating test post:', error.response?.data || error.message);
    }
};

testImageUpload();
