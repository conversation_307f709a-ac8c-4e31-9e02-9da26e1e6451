// File: backend/test-admin-users.js
// Script để test các API quản lý người dùng cho admin

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Token admin (cần thay thế bằng token thực tế)
const ADMIN_TOKEN = 'your-admin-token-here';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
    }
});

// Test functions
async function testGetAllUsers() {
    console.log('\n=== Test: Get All Users ===');
    try {
        const response = await api.get('/admin/users', {
            params: {
                page: 1,
                limit: 5,
                search: '',
                role: '',
                status: ''
            }
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testGetUserStats() {
    console.log('\n=== Test: Get User Stats ===');
    try {
        const response = await api.get('/admin/users/stats');
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testGetUserById(userId) {
    console.log('\n=== Test: Get User By ID ===');
    try {
        const response = await api.get(`/admin/users/${userId}`);
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testWarnUser(userId) {
    console.log('\n=== Test: Warn User ===');
    try {
        const response = await api.post(`/admin/users/${userId}/warn`, {
            message: 'Đây là cảnh báo test',
            reason: 'Vi phạm quy định diễn đàn'
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testSuspendUser(userId) {
    console.log('\n=== Test: Suspend User ===');
    try {
        const response = await api.put(`/admin/users/${userId}/suspend`, {
            reason: 'Tạm khóa do vi phạm nghiêm trọng',
            suspendedUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 ngày
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testActivateUser(userId) {
    console.log('\n=== Test: Activate User ===');
    try {
        const response = await api.put(`/admin/users/${userId}/activate`);
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

async function testUpdateUserRole(userId) {
    console.log('\n=== Test: Update User Role ===');
    try {
        const response = await api.put(`/admin/users/${userId}/role`, {
            role: 'editor'
        });
        console.log('✅ Success:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.response?.data || error.message);
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting Admin User Management API Tests...');
    console.log('⚠️  Make sure to replace ADMIN_TOKEN with a valid admin token');
    
    // Test basic endpoints
    await testGetUserStats();
    await testGetAllUsers();
    
    // Note: Replace 'USER_ID_HERE' with actual user ID for testing
    const testUserId = 'USER_ID_HERE';
    
    if (testUserId !== 'USER_ID_HERE') {
        await testGetUserById(testUserId);
        await testWarnUser(testUserId);
        await testSuspendUser(testUserId);
        await testActivateUser(testUserId);
        await testUpdateUserRole(testUserId);
    } else {
        console.log('\n⚠️  Skipping user-specific tests. Please provide a valid user ID.');
    }
    
    console.log('\n✨ Tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testGetAllUsers,
    testGetUserStats,
    testGetUserById,
    testWarnUser,
    testSuspendUser,
    testActivateUser,
    testUpdateUserRole
};
