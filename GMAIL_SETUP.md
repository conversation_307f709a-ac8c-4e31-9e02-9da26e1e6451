# 📧 Gmail Setup Guide for Email Features

## 🎉 **Email Features Now Active!**

✅ **Nodemailer installed successfully**  
✅ **Email templates enabled**  
✅ **Beautiful HTML emails ready**  

## 🔧 **Gmail Configuration Steps:**

### **Step 1: Enable 2-Factor Authentication**

1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click **Security** in the left sidebar
3. Under "Signing in to Google", click **2-Step Verification**
4. Click **Get Started** and follow the setup process
5. Verify with your phone number

### **Step 2: Generate App Password**

1. After enabling 2FA, go back to **Security**
2. Under "Signing in to Google", click **App passwords**
3. Select app: **Mail**
4. Select device: **Other (Custom name)**
5. Enter name: **TVU Forum Backend**
6. Click **Generate**
7. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### **Step 3: Update .env File**

Edit `backend/.env` file:

```env
# Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd efgh ijkl mnop
FRONTEND_URL=http://localhost:5173
```

**Example:**
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd efgh ijkl mnop
FRONTEND_URL=http://localhost:5173
```

### **Step 4: Restart Backend Server**

```bash
# Stop current server (Ctrl+C)
# Then restart
cd backend
npm start
```

## 🧪 **Test Email Features:**

### **1. Test Registration Email:**
1. Go to `http://localhost:5173/register`
2. Fill form and submit
3. Check console for "✅ Verification email sent successfully!"
4. Check your Gmail inbox for verification email
5. Click verification link

### **2. Test Password Reset Email:**
1. Go to `http://localhost:5173/forgot-password`
2. Enter your email
3. Check console for "✅ Password reset email sent successfully!"
4. Check your Gmail inbox for reset email
5. Click reset link and set new password

## 📧 **Email Templates Preview:**

### **Registration Email:**
- 🎨 Beautiful header with icon
- 🔐 Verification button
- ⏰ 24-hour expiry notice
- 📱 Mobile-responsive design

### **Password Reset Email:**
- 🎨 Professional design
- 🔑 Reset password button
- ⚠️ 10-minute expiry warning
- 🔒 Security notices

## 🔍 **Troubleshooting:**

### **Common Issues:**

**1. "Invalid credentials" error:**
- ✅ Make sure 2FA is enabled
- ✅ Use App Password, not regular password
- ✅ Remove spaces from app password

**2. "Less secure app access" error:**
- ✅ Use App Password instead
- ✅ Don't enable "Less secure app access"

**3. Email not received:**
- ✅ Check spam/junk folder
- ✅ Verify email address is correct
- ✅ Check console for error messages

**4. "Authentication failed" error:**
```bash
# Check .env file format:
EMAIL_USER=<EMAIL>
EMAIL_PASS=yourapppassword
# No quotes, no spaces around =
```

## 🎯 **Production Setup:**

### **For Production Deployment:**

1. **Use environment variables:**
```bash
export EMAIL_USER=<EMAIL>
export EMAIL_PASS=your-app-password
export FRONTEND_URL=https://your-domain.com
```

2. **Consider using dedicated email service:**
- SendGrid
- Mailgun
- Amazon SES
- Postmark

3. **Security best practices:**
- Use strong app passwords
- Rotate passwords regularly
- Monitor email sending logs
- Set up rate limiting

## ✅ **Verification Checklist:**

- [ ] 2FA enabled on Gmail
- [ ] App password generated
- [ ] .env file updated with correct credentials
- [ ] Backend server restarted
- [ ] Registration email test successful
- [ ] Password reset email test successful
- [ ] Email verification flow working
- [ ] Password reset flow working

## 🚀 **Ready to Go!**

**Your email system is now fully operational! 🎉**

**Features working:**
- ✅ Email verification on registration
- ✅ Password reset via email
- ✅ Beautiful HTML email templates
- ✅ Mobile-responsive emails
- ✅ Security with token expiry
- ✅ Professional UI/UX

**Test the complete flow and enjoy your fully functional authentication system! 🔐✨📧**
