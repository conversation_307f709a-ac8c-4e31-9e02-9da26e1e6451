// Test thumbnail extraction from post content
require('dotenv').config();
const mongoose = require('mongoose');
const Post = require('./models/Post');
const { extractFirstImageFromContent, getPostThumbnail } = require('./utils/imageExtractor');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
};

const testThumbnailExtraction = async () => {
    await connectDB();
    
    console.log('\n🖼️ Testing Thumbnail Extraction...\n');
    
    try {
        // Get some posts to test
        const posts = await Post.find().limit(10);
        
        console.log(`📊 Found ${posts.length} posts to test\n`);
        
        posts.forEach((post, index) => {
            console.log(`${index + 1}. "${post.title}"`);
            console.log(`   📝 Content length: ${post.content?.length || 0} characters`);
            
            // Test thumbnail extraction
            const thumbnail = getPostThumbnail(post);
            const firstImage = extractFirstImageFromContent(post.content);
            
            console.log(`   🖼️ Thumbnail: ${thumbnail || 'No thumbnail found'}`);
            console.log(`   🎯 First image: ${firstImage || 'No image found'}`);
            
            // Check if content has images
            const hasImages = post.content && post.content.includes('<img');
            console.log(`   📷 Has images in content: ${hasImages ? 'YES' : 'NO'}`);
            
            if (hasImages) {
                // Extract all img tags for debugging
                const imgMatches = post.content.match(/<img[^>]+>/g);
                console.log(`   🔍 Found ${imgMatches?.length || 0} img tags`);
                
                if (imgMatches && imgMatches.length > 0) {
                    console.log(`   📋 First img tag: ${imgMatches[0].substring(0, 100)}...`);
                }
            }
            
            console.log('   ' + '-'.repeat(50));
        });
        
        // Test with sample HTML content
        console.log('\n🧪 Testing with sample HTML content:\n');
        
        const sampleContent = `
            <p>This is a test post with images.</p>
            <img src="/upload/image1.jpg" alt="First image" />
            <p>Some text between images.</p>
            <img src="http://localhost:5000/upload/image2.png" alt="Second image" />
            <p>More content here.</p>
        `;
        
        const sampleThumbnail = extractFirstImageFromContent(sampleContent);
        console.log(`📝 Sample content thumbnail: ${sampleThumbnail}`);
        
        // Test with external URLs
        const externalContent = `
            <p>Post with external image.</p>
            <img src="https://example.com/image.jpg" alt="External image" />
            <p>End of post.</p>
        `;
        
        const externalThumbnail = extractFirstImageFromContent(externalContent);
        console.log(`🌐 External content thumbnail: ${externalThumbnail}`);
        
        // Test with no images
        const noImageContent = `
            <p>This post has no images.</p>
            <p>Just text content here.</p>
        `;
        
        const noImageThumbnail = extractFirstImageFromContent(noImageContent);
        console.log(`❌ No image content thumbnail: ${noImageThumbnail || 'null (as expected)'}`);
        
    } catch (error) {
        console.error('❌ Error testing thumbnail extraction:', error);
    }
    
    mongoose.connection.close();
};

testThumbnailExtraction();
