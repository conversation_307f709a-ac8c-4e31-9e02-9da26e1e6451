# 🔧 Google OAuth Error Fix Guide

## ❌ **Current Error:**
```
Wrong recipient, payload audience != requiredAudience
FedCM was disabled either temporarily based on previous user action
The given origin is not allowed for the given client ID
```

## ✅ **Fixes Applied:**

### 🔑 **1. Backend Configuration Fixed**
```bash
# backend/.env - Updated with correct Client ID
GOOGLE_CLIENT_ID=************-jdm9kngkj7lfmkjl1pqake1hbhfju9tt.apps.googleusercontent.com
```

### 🛠️ **2. Enhanced Error Handling**
```javascript
// backend/controllers/googleAuthController.js
if (error.message.includes('Wrong recipient') || error.message.includes('audience')) {
    return res.status(400).json({
        success: false,
        message: 'Google Client ID mismatch. Please check configuration.'
    });
}
```

### 🔍 **3. Debug Tool Created**
- **URL**: `http://localhost:5174/test/google-oauth`
- **Features**: 
  - Test Google login
  - Show JWT payload
  - Verify Client ID match
  - Display user information
  - Copy token for testing

## 🚀 **Next Steps to Fix:**

### **Step 1: Google Cloud Console Setup**
1. **Go to**: https://console.cloud.google.com/
2. **Select Project**: Your existing project
3. **Navigate to**: APIs & Services > Credentials
4. **Find**: OAuth 2.0 Client ID

### **Step 2: Update Authorized Origins**
```
Authorized JavaScript origins:
✅ http://localhost:5174
✅ http://localhost:3000  
✅ https://yourdomain.com (for production)

Authorized redirect URIs:
✅ http://localhost:5174/auth/google/callback
✅ https://yourdomain.com/auth/google/callback
```

### **Step 3: Check Domain Authorization**
- **Current domain**: `http://localhost:5174`
- **Must be added** to authorized origins
- **Case sensitive** - must match exactly

### **Step 4: Browser Settings**
```
Chrome Settings > Privacy and Security > Site Settings > 
Third-party sign-in > Allow sites to ask to sign you in
```

### **Step 5: Clear Browser Data**
```
1. Clear cookies for localhost:5174
2. Clear localStorage
3. Restart browser
4. Try again
```

## 🔍 **Debug Process:**

### **1. Use Debug Tool**
```
1. Navigate to: http://localhost:5174/test/google-oauth
2. Click Google Login button
3. Check console for detailed logs
4. Verify Client ID match
5. Copy token if needed
```

### **2. Backend Test**
```bash
# Test backend configuration
cd backend
node test-google-oauth.js
```

### **3. Manual Token Test**
```javascript
// In browser console after getting token
fetch('http://localhost:5000/api/auth/google-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ credential: 'YOUR_TOKEN_HERE' })
})
.then(res => res.json())
.then(data => console.log(data));
```

## 📋 **Checklist:**

### **✅ Configuration**
- [x] Backend .env has correct GOOGLE_CLIENT_ID
- [x] Frontend App.jsx has same Client ID
- [ ] Google Cloud Console authorized origins updated
- [ ] Browser third-party cookies enabled

### **✅ Code Updates**
- [x] Enhanced error handling in backend
- [x] Better error messages in frontend
- [x] Debug tool created
- [x] Logging added for troubleshooting

### **✅ Testing**
- [x] Backend configuration test script
- [x] Frontend debug component
- [ ] End-to-end login flow test
- [ ] Production environment test

## 🎯 **Common Solutions:**

### **Problem 1: "Wrong recipient" Error**
**Cause**: Client ID mismatch between frontend and backend
**Solution**: Ensure both use same Client ID

### **Problem 2: "Origin not allowed" Error**  
**Cause**: Domain not in authorized origins
**Solution**: Add `http://localhost:5174` to Google Cloud Console

### **Problem 3: "FedCM disabled" Error**
**Cause**: Browser blocked third-party sign-in
**Solution**: Enable in browser settings

### **Problem 4: CORS Issues**
**Cause**: Cross-origin requests blocked
**Solution**: Check backend CORS configuration

## 🔧 **Files Modified:**

```
backend/
├── .env                           # Updated GOOGLE_CLIENT_ID
├── controllers/googleAuthController.js  # Enhanced error handling
└── test-google-oauth.js          # New test script

frontend/
├── src/App.jsx                   # Added debug route
├── src/context/AuthContext.jsx   # Better error handling
└── src/components/GoogleOAuthDebug.jsx  # New debug tool
```

## 🌐 **Test URLs:**

### **Debug Tool**
```
http://localhost:5174/test/google-oauth
```

### **Backend Test**
```
http://localhost:5000/api/auth/google-login
```

### **Google Cloud Console**
```
https://console.cloud.google.com/apis/credentials
```

## 📞 **Support Commands:**

### **Check Backend Logs**
```bash
cd backend
npm run dev
# Watch for Google OAuth logs
```

### **Check Frontend Console**
```javascript
// In browser console
localStorage.clear();
// Then try login again
```

### **Test Network**
```bash
# Test backend connectivity
curl http://localhost:5000/api/auth/google-login
```

---

## ✅ **Summary:**

1. **✅ Fixed**: Backend Client ID configuration
2. **✅ Added**: Enhanced error handling
3. **✅ Created**: Debug tool for testing
4. **🔄 Next**: Update Google Cloud Console settings
5. **🔄 Test**: Use debug tool to verify fix

**🎯 Main issue**: Google Cloud Console authorized origins need to include `http://localhost:5174`

**🚀 Quick fix**: Go to Google Cloud Console → Credentials → Add authorized origin → Test again
