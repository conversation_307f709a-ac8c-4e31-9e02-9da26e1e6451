// File: backend/test-analytics-api.js
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test login and get token
async function loginAndGetToken() {
    try {
        console.log('🔐 Logging in as admin...');
        
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
            email: '<EMAIL>',
            password: 'admin123'
        });

        if (loginResponse.data.success) {
            console.log('✅ Login successful');
            return loginResponse.data.token;
        } else {
            console.error('❌ Login failed:', loginResponse.data.message);
            return null;
        }
    } catch (error) {
        console.error('❌ Login error:', error.response?.data || error.message);
        return null;
    }
}

// Test analytics APIs
async function testAnalyticsAPIs(token) {
    const headers = { Authorization: `Bearer ${token}` };
    
    console.log('\n📊 Testing Analytics APIs...');

    // Test Overview API
    try {
        console.log('\n1. Testing Overview API...');
        const overviewResponse = await axios.get(`${API_BASE_URL}/admin/analytics/overview`, {
            headers,
            params: { days: 30 }
        });
        
        console.log('✅ Overview API Success');
        console.log('Data:', JSON.stringify(overviewResponse.data, null, 2));
    } catch (error) {
        console.error('❌ Overview API Error:', error.response?.data || error.message);
    }

    // Test User Activity API
    try {
        console.log('\n2. Testing User Activity API...');
        const userActivityResponse = await axios.get(`${API_BASE_URL}/admin/analytics/user-activity`, {
            headers,
            params: { days: 30 }
        });
        
        console.log('✅ User Activity API Success');
        console.log('Data:', JSON.stringify(userActivityResponse.data, null, 2));
    } catch (error) {
        console.error('❌ User Activity API Error:', error.response?.data || error.message);
    }

    // Test Popular Content API
    try {
        console.log('\n3. Testing Popular Content API...');
        const popularContentResponse = await axios.get(`${API_BASE_URL}/admin/analytics/popular-content`, {
            headers,
            params: { days: 30 }
        });
        
        console.log('✅ Popular Content API Success');
        console.log('Data:', JSON.stringify(popularContentResponse.data, null, 2));
    } catch (error) {
        console.error('❌ Popular Content API Error:', error.response?.data || error.message);
    }

    // Test Growth Trends API
    try {
        console.log('\n4. Testing Growth Trends API...');
        const growthTrendsResponse = await axios.get(`${API_BASE_URL}/admin/analytics/growth-trends`, {
            headers,
            params: { days: 30 }
        });
        
        console.log('✅ Growth Trends API Success');
        console.log('Data:', JSON.stringify(growthTrendsResponse.data, null, 2));
    } catch (error) {
        console.error('❌ Growth Trends API Error:', error.response?.data || error.message);
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting Analytics API Tests...');
    
    const token = await loginAndGetToken();
    if (!token) {
        console.error('❌ Cannot proceed without valid token');
        return;
    }

    await testAnalyticsAPIs(token);
    
    console.log('\n🎉 Analytics API Tests Completed!');
}

// Run tests
runTests().catch(error => {
    console.error('❌ Test error:', error);
});
