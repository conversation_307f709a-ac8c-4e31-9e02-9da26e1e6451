# <PERSON><PERSON> thống <PERSON>uản lý Chatbot cho Admin

## Tổng quan
Hệ thống quản lý chatbot tích hợp với Google Dialogflow, cho phép admin:
- Quản lý intents (tạo, sửa, xóa)
- <PERSON><PERSON>n luyện và cải thiện chatbot
- <PERSON> dõi conversations và phản hồi
- <PERSON><PERSON> tích hiệu suất chatbot
- Sync với Dialogflow

## Các file đã tạo/chỉnh sửa

### Backend Models

1. **backend/models/ChatbotIntent.js** - Mới
   - Quản lý intents với training phrases và responses
   - Sync status với Dialogflow
   - Thống kê trigger count và success rate
   - Categories và tags

2. **backend/models/ChatbotConversation.js** - Mới
   - Lưu trữ lịch sử conversations
   - Messages với detected intents
   - Feedback và ratings từ người dùng
   - Session info và device detection

### Backend Services & Controllers

3. **backend/services/dialogflowService.js** - <PERSON><PERSON>i
   - <PERSON><PERSON><PERSON> hợ<PERSON> với Google Dialogflow API
   - Detect intent, create/update/delete intents
   - Train agent và export/import
   - Session management

4. **backend/controllers/adminChatbotController.js** - Mới
   - CRUD operations cho intents
   - Conversation management
   - Analytics và reporting
   - Sync với Dialogflow

5. **backend/routes/adminChatbotRoutes.js** - Mới
   - Routes cho tất cả chatbot APIs
   - Phân quyền admin

6. **backend/index.js** - Đã cập nhật
   - Thêm chatbot routes

### Frontend

7. **frontend/src/pages/admin/AdminChatbotPage.jsx** - Mới
   - Giao diện quản lý chatbot với 3 tabs
   - Quản lý intents với dialog tạo/sửa
   - Xem conversations và chi tiết
   - Dashboard analytics với biểu đồ

8. **frontend/src/layouts/AdminDashboard.jsx** - Đã cập nhật
   - Thêm route chatbot

9. **frontend/src/pages/admin/Sidebar.jsx** - Đã cập nhật
   - Thêm menu "Quản lý Chatbot"

### Scripts & Utilities

10. **backend/test-chatbot.js** - Mới
    - Test suite cho chatbot APIs
    - Test CRUD operations và analytics

11. **backend/generate-sample-chatbot-data.js** - Mới
    - Tạo dữ liệu mẫu cho intents và conversations
    - Sample intents cho FAQ, greeting, support

## API Endpoints

### Intent Management
```
GET    /api/admin/chatbot/intents              - Lấy danh sách intents
GET    /api/admin/chatbot/intents/:id          - Lấy chi tiết intent
POST   /api/admin/chatbot/intents              - Tạo intent mới
PUT    /api/admin/chatbot/intents/:id          - Cập nhật intent
DELETE /api/admin/chatbot/intents/:id          - Xóa intent
POST   /api/admin/chatbot/intents/:id/training-phrases  - Thêm training phrase
POST   /api/admin/chatbot/intents/:id/responses         - Thêm response
```

### Dialogflow Management
```
POST   /api/admin/chatbot/sync                 - Sync với Dialogflow
POST   /api/admin/chatbot/train                - Huấn luyện agent
```

### Conversation Management
```
GET    /api/admin/chatbot/conversations        - Lấy danh sách conversations
GET    /api/admin/chatbot/conversations/:id    - Lấy chi tiết conversation
PUT    /api/admin/chatbot/conversations/:id/review     - Đánh dấu đã review
POST   /api/admin/chatbot/conversations/:id/notes      - Thêm ghi chú
```

### Analytics
```
GET    /api/admin/chatbot/analytics/overview        - Thống kê tổng quan
GET    /api/admin/chatbot/analytics/intents         - Thống kê intents
GET    /api/admin/chatbot/analytics/conversations   - Thống kê conversations
```

## Cấu hình Dialogflow

### 1. Tạo Google Cloud Project
```bash
# Tạo project trên Google Cloud Console
# Enable Dialogflow API
# Tạo Service Account và download JSON key
```

### 2. Environment Variables
```bash
# Thêm vào .env
DIALOGFLOW_PROJECT_ID=your-project-id
DIALOGFLOW_LANGUAGE_CODE=vi
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
```

### 3. Cài đặt dependencies
```bash
cd backend
npm install @google-cloud/dialogflow uuid
```

## Cách sử dụng

### 1. Khởi động hệ thống
```bash
# Backend
cd backend
npm start

# Frontend
cd frontend
npm run dev
```

### 2. Tạo dữ liệu mẫu
```bash
cd backend
# Tạo tất cả dữ liệu mẫu
node generate-sample-chatbot-data.js

# Chỉ tạo intents
node generate-sample-chatbot-data.js --intents-only

# Chỉ tạo conversations
node generate-sample-chatbot-data.js --conversations-only

# Chỉ cập nhật stats
node generate-sample-chatbot-data.js --stats-only
```

### 3. Test APIs
```bash
cd backend
# Test tất cả
node test-chatbot.js

# Test specific
node test-chatbot.js --intents-only
node test-chatbot.js --analytics-only
node test-chatbot.js --conversations-only
```

### 4. Truy cập giao diện
- Đăng nhập với tài khoản admin
- Truy cập `/admin/chatbot`
- Sử dụng 3 tabs: Intents, Conversations, Analytics

## Tính năng chính

### 1. Quản lý Intents
- **Tạo intent mới**: Tên, training phrases, responses
- **Categories**: greeting, faq, support, information, navigation, feedback
- **Training phrases**: Nhiều câu huấn luyện cho mỗi intent
- **Responses**: Nhiều phản hồi khác nhau
- **Sync status**: Theo dõi trạng thái sync với Dialogflow

### 2. Conversation Management
- **Lịch sử conversations**: Xem tất cả cuộc hội thoại
- **Chi tiết messages**: User input và bot response
- **Detected intents**: Intent được nhận diện và confidence
- **Feedback**: Rating và comment từ người dùng
- **Review system**: Đánh dấu conversations cần review

### 3. Analytics Dashboard
- **Overview stats**: Tổng intents, conversations, ratings
- **Popular intents**: Intent được trigger nhiều nhất
- **Failed intents**: Câu hỏi thất bại nhiều nhất
- **Intent by category**: Phân bố theo danh mục
- **Conversation trends**: Xu hướng theo thời gian
- **Device analytics**: Thống kê theo thiết bị

### 4. Dialogflow Integration
- **Auto sync**: Tự động sync intents với Dialogflow
- **Train agent**: Huấn luyện lại model
- **Error handling**: Xử lý lỗi sync và hiển thị status
- **Batch operations**: Sync nhiều intents cùng lúc

## Workflow quản lý

### 1. Tạo Intent mới
1. Click "Tạo Intent" trong tab Intents
2. Điền thông tin: tên, danh mục, mô tả
3. Thêm training phrases (ít nhất 3-5 câu)
4. Thêm responses (ít nhất 1-2 phản hồi)
5. Save → Tự động sync với Dialogflow

### 2. Cải thiện Chatbot
1. Xem "Failed intents" trong Analytics
2. Tạo intent mới cho câu hỏi thất bại
3. Hoặc thêm training phrases vào intent hiện có
4. Sync và train lại agent

### 3. Review Conversations
1. Xem conversations có rating thấp
2. Phân tích lý do thất bại
3. Cải thiện intent hoặc tạo intent mới
4. Đánh dấu đã review

### 4. Monitor Performance
1. Theo dõi success rate của intents
2. Xem popular intents để hiểu nhu cầu user
3. Phân tích conversation trends
4. Cải thiện dựa trên feedback

## Sample Intents được tạo

1. **greeting** - Chào hỏi cơ bản
2. **how_to_register** - Hướng dẫn đăng ký
3. **how_to_post** - Hướng dẫn đăng bài
4. **contact_support** - Liên hệ hỗ trợ
5. **forum_rules** - Quy định diễn đàn
6. **popular_topics** - Chủ đề phổ biến
7. **goodbye** - Tạm biệt

## Troubleshooting

### Lỗi thường gặp

1. **Dialogflow connection error**
   - Kiểm tra GOOGLE_APPLICATION_CREDENTIALS
   - Verify project ID và permissions
   - Enable Dialogflow API

2. **Sync failed**
   - Xem sync error trong intent detail
   - Check network connectivity
   - Verify service account permissions

3. **No conversations data**
   - Chạy generate sample data script
   - Check conversation logging

### Debug
```bash
# Kiểm tra dữ liệu
mongo
use your_database
db.chatbotintents.count()
db.chatbotconversations.count()

# Test Dialogflow connection
node -e "
const service = require('./services/dialogflowService');
service.validateConfig();
console.log('Config OK');
"
```

## Mở rộng tương lai

1. **Rich responses**: Cards, quick replies, images
2. **Context management**: Conversation context và follow-up
3. **Webhook integration**: Custom fulfillment logic
4. **Multi-language**: Hỗ trợ nhiều ngôn ngữ
5. **Voice integration**: Text-to-speech và speech-to-text
6. **AI training**: Auto-generate training phrases
7. **A/B testing**: Test different responses
8. **Integration**: Kết nối với các platform khác (Facebook, Telegram)

## Best Practices

### Intent Design
- Mỗi intent nên có ít nhất 10-20 training phrases
- Training phrases nên đa dạng về cách diễn đạt
- Responses nên ngắn gọn và hữu ích
- Sử dụng categories để tổ chức intents

### Conversation Management
- Review conversations có rating thấp thường xuyên
- Phân tích failed intents để cải thiện
- Thêm training phrases từ user input thực tế
- Monitor conversation trends để hiểu user behavior

### Performance Optimization
- Sync intents theo batch thay vì từng cái
- Cache frequently used data
- Use pagination cho large datasets
- Monitor API quotas và limits
