# 🔐 Google OAuth Login Implementation

## 🎯 **Complete Google Login Implementation**

<PERSON><PERSON> implement hoàn chỉnh Google OAuth login với backend verification và frontend integration.

### ✅ **Implementation Overview**

#### **Backend Components**
- **Google Auth Controller**: Verify Google tokens và create/update users
- **Updated User Model**: Support Google OAuth fields
- **Auth Routes**: Complete Google login endpoints
- **JWT Integration**: Generate app tokens after Google verification

#### **Frontend Components**
- **AuthContext**: Google login function
- **Login Page**: Updated to use AuthContext
- **Google OAuth Provider**: Configured with client ID

## 🔧 **Backend Implementation**

### **1. Google Auth Controller**
```javascript
// backend/controllers/googleAuthController.js
const { OAuth2Client } = require('google-auth-library');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

exports.googleLogin = async (req, res) => {
    try {
        const { credential } = req.body;
        
        // Verify Google token
        const ticket = await client.verifyIdToken({
            idToken: credential,
            audience: process.env.GOOGLE_CLIENT_ID
        });

        const payload = ticket.getPayload();
        const { sub: googleId, email, name: fullName, picture: avatarUrl } = payload;

        // Find or create user
        let user = await User.findOne({ 
            $or: [{ email }, { googleId }]
        });

        if (!user) {
            // Create new user
            user = new User({
                googleId, email, fullName, avatarUrl,
                username: email.split('@')[0],
                isEmailVerified: true,
                authProvider: 'google'
            });
            await user.save();
        }

        // Generate JWT token
        const token = jwt.sign(
            { id: user._id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        res.status(200).json({
            success: true,
            token,
            user: {
                id: user._id,
                email: user.email,
                fullName: user.fullName,
                username: user.username,
                avatarUrl: user.avatarUrl,
                authProvider: 'google'
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Google login failed'
        });
    }
};
```

### **2. Updated User Model**
```javascript
// backend/models/User.js
const userSchema = new mongoose.Schema({
    fullName: String,
    email: { type: String, unique: true },
    password: String,
    username: { type: String, unique: true, sparse: true },
    avatarUrl: { type: String, default: '' },
    role: { type: String, enum: ['user', 'editor', 'admin'], default: 'user' },
    
    // Google OAuth fields
    googleId: { type: String, unique: true, sparse: true },
    authProvider: { type: String, enum: ['local', 'google'], default: 'local' },
    isEmailVerified: { type: Boolean, default: false },
}, { timestamps: true });
```

### **3. Auth Routes**
```javascript
// backend/routes/authRoutes.js
const { googleLogin, getGoogleAuthUrl } = require('../controllers/googleAuthController');

router.post('/google-login', googleLogin);
router.get('/google-auth-url', getGoogleAuthUrl);
```

## 🎨 **Frontend Implementation**

### **1. AuthContext Google Login**
```javascript
// frontend/src/context/AuthContext.jsx
const googleLogin = async (credential) => {
    try {
        const response = await fetch('http://localhost:5000/api/auth/google-login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ credential }),
        });

        const data = await response.json();
        
        if (data.success) {
            localStorage.setItem('user', JSON.stringify(data.user));
            localStorage.setItem('token', data.token);
            setUser(data.user);
            setToken(data.token);
            return { success: true, user: data.user };
        }
        
        return { success: false, error: data.message };
    } catch (error) {
        return { success: false, error: 'Network error' };
    }
};
```

### **2. Login Page Integration**
```javascript
// frontend/src/pages/Login.jsx
const { login, googleLogin } = useContext(AuthContext);

const handleGoogleLoginSuccess = async (credentialResponse) => {
    try {
        const result = await googleLogin(credentialResponse.credential);
        
        if (result.success) {
            if (result.user.role === 'admin') {
                navigate('/admin');
            } else {
                navigate('/');
            }
        } else {
            alert(result.error);
        }
    } catch (err) {
        alert('Đăng nhập Google thất bại');
    }
};
```

## 🔑 **Google OAuth Setup Required**

### **Step 1: Google Cloud Console Setup**
1. **Go to**: https://console.cloud.google.com/
2. **Create Project**: "TVU Forum" hoặc tên project của bạn
3. **Enable APIs**: Google+ API và Google OAuth2 API
4. **Create Credentials**: OAuth 2.0 Client ID

### **Step 2: OAuth Client Configuration**
```
Application Type: Web Application
Name: TVU Forum Web Client

Authorized JavaScript Origins:
- http://localhost:5174
- http://localhost:3000
- https://yourdomain.com (production)

Authorized Redirect URIs:
- http://localhost:5174/auth/google/callback
- https://yourdomain.com/auth/google/callback (production)
```

### **Step 3: Environment Variables**
```bash
# backend/.env
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5174/auth/google/callback
JWT_SECRET=your-jwt-secret-key
```

### **Step 4: Frontend Configuration**
```javascript
// frontend/src/App.jsx
<GoogleOAuthProvider clientId="your-google-client-id.apps.googleusercontent.com">
    <AuthProvider>
        {/* Your app components */}
    </AuthProvider>
</GoogleOAuthProvider>
```

## 📋 **Testing Checklist**

### **✅ Backend Testing**
```bash
# Test Google login endpoint
curl -X POST http://localhost:5000/api/auth/google-login \
  -H "Content-Type: application/json" \
  -d '{"credential":"google-jwt-token"}'

# Expected response:
{
  "success": true,
  "token": "your-app-jwt-token",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "fullName": "User Name",
    "authProvider": "google"
  }
}
```

### **✅ Frontend Testing**
1. **Click Google Login Button**: Should open Google OAuth popup
2. **Select Google Account**: Should authenticate with Google
3. **Receive Credential**: Should get JWT token from Google
4. **Send to Backend**: Should verify token và create/login user
5. **Store Token**: Should save app token và user data
6. **Navigate**: Should redirect to home hoặc admin page

## 🎯 **Security Features**

### **✅ Token Verification**
- **Google Token**: Verified using Google's OAuth2Client
- **App Token**: Generated using JWT với expiration
- **User Creation**: Automatic user creation for new Google users
- **Email Verification**: Google users are pre-verified

### **✅ Error Handling**
- **Invalid Tokens**: Proper error messages
- **Network Errors**: Graceful fallbacks
- **Database Errors**: Safe error responses
- **Duplicate Users**: Handle existing users correctly

## 🚀 **Next Steps**

### **1. Get Google OAuth Credentials**
- Create Google Cloud project
- Configure OAuth consent screen
- Generate client ID và client secret

### **2. Update Environment Variables**
```bash
# Replace with your actual credentials
GOOGLE_CLIENT_ID=your-actual-client-id
GOOGLE_CLIENT_SECRET=your-actual-client-secret
```

### **3. Test Complete Flow**
- Start backend server
- Start frontend server
- Test Google login button
- Verify user creation in database

### **4. Production Deployment**
- Add production domains to Google OAuth
- Update environment variables for production
- Test with HTTPS domains

---

**🔐 Google OAuth Login Implementation Complete!**

**Backend**: Token verification, user creation, JWT generation
**Frontend**: AuthContext integration, login page updates
**Security**: Proper token verification và error handling
**Database**: User model updated for Google OAuth

**🔑 Next: Get Google OAuth credentials và update environment variables**

**Google login sẽ work sau khi setup OAuth credentials!** ✅🎯
