# Test UI Comments - Hướng dẫn kiểm tra

## 🧪 Các test cases cần kiểm tra

### 1. Basic Comment Functionality
- [ ] Tạo comment mới
- [ ] Hi<PERSON>n thị danh sách comments
- [ ] Xóa comment (chỉ author)
- [ ] Real-time updates khi có comment mới

### 2. Nested Replies
- [ ] Reply vào root comment
- [ ] Reply vào reply (level 2)
- [ ] Reply vào reply level 2 (level 3)
- [ ] Reply vào reply level 3+ (deep nesting)
- [ ] Kiểm tra visual hierarchy

### 3. Like System
- [ ] Like root comment
- [ ] Like nested reply
- [ ] Unlike comment/reply
- [ ] Real-time like updates
- [ ] Prevent double-clicking

### 4. UI/UX Features
- [ ] Expand/collapse replies
- [ ] Visual indentation
- [ ] Level indicators
- [ ] Responsive design
- [ ] Hover effects
- [ ] Loading states

### 5. Edge Cases
- [ ] Very deep nesting (6+ levels)
- [ ] Long comment content
- [ ] Many replies (100+)
- [ ] Network errors
- [ ] Socket.IO disconnection

## 🎯 Test Scenarios

### Scenario 1: Basic Comment Flow
1. Mở một post detail page
2. Mở comment dialog
3. V<PERSON>ết comment: "This is a test comment"
4. Submit comment
5. Verify comment appears immediately
6. Verify real-time update (open in another tab)

### Scenario 2: Nested Replies
1. Click "Trả lời" trên root comment
2. Viết reply: "This is a reply to root"
3. Submit reply
4. Click "Trả lời" trên reply vừa tạo
5. Viết nested reply: "This is a nested reply"
6. Submit và verify hierarchy

### Scenario 3: Deep Nesting
1. Tạo chain of replies: Root → Reply1 → Reply2 → Reply3 → Reply4
2. Verify visual indentation giảm dần
3. Verify level indicators hiển thị đúng
4. Verify UI không bị break ở deep levels

### Scenario 4: Like System
1. Like một root comment
2. Verify heart icon đổi màu đỏ
3. Verify count tăng lên
4. Like một nested reply
5. Unlike comment
6. Verify real-time updates

### Scenario 5: Expand/Collapse
1. Tạo comment với nhiều replies
2. Click "Ẩn X trả lời"
3. Verify replies bị ẩn
4. Click "Xem X trả lời"
5. Verify replies hiển thị lại

## 🔍 Visual Checks

### Indentation & Hierarchy
```
Root Comment (no indent)
├── Reply Level 1 (small indent)
│   ├── Reply Level 2 (medium indent)
│   │   └── Reply Level 3 (large indent)
│   └── Another Level 2
└── Another Level 1
```

### Color Scheme
- Root comments: Lighter background
- Level 1 replies: Slightly darker
- Level 2+ replies: Alternating backgrounds
- Borders: Different colors per level

### Typography
- Root comments: Larger font, bigger avatar
- Nested replies: Smaller font, smaller avatar
- Level indicators: Clear and readable

## 🐛 Common Issues to Check

### Performance Issues
- [ ] Lag khi render nhiều comments
- [ ] Memory leaks với deep nesting
- [ ] Slow scrolling với long comment lists

### UI Bugs
- [ ] Overlapping elements
- [ ] Broken indentation
- [ ] Missing borders
- [ ] Incorrect spacing

### Functional Bugs
- [ ] Like button không hoạt động
- [ ] Reply button không response
- [ ] Delete không xóa nested replies
- [ ] Real-time updates bị miss

### Mobile Responsiveness
- [ ] Comments hiển thị đúng trên mobile
- [ ] Touch interactions hoạt động
- [ ] Indentation không quá sâu
- [ ] Text readable

## 📱 Device Testing

### Desktop
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

### Mobile
- [ ] iOS Safari
- [ ] Android Chrome
- [ ] Responsive breakpoints

### Tablet
- [ ] iPad
- [ ] Android tablets

## 🚀 Performance Testing

### Load Testing
1. Tạo 100+ comments với nested replies
2. Measure render time
3. Check memory usage
4. Test scroll performance

### Network Testing
1. Test với slow 3G
2. Test với network interruptions
3. Test Socket.IO reconnection

## ✅ Acceptance Criteria

### Must Have
- ✅ Nested replies work unlimited levels
- ✅ Like system works for all comments
- ✅ Real-time updates work
- ✅ UI is responsive and readable
- ✅ No major performance issues

### Nice to Have
- ✅ Smooth animations
- ✅ Good visual hierarchy
- ✅ Intuitive UX
- ✅ Mobile optimized

## 🎯 Test Results Template

```
Date: ___________
Tester: ___________
Browser: ___________
Device: ___________

Basic Comments: ✅/❌
Nested Replies: ✅/❌
Like System: ✅/❌
UI/UX: ✅/❌
Performance: ✅/❌
Mobile: ✅/❌

Issues Found:
1. ___________
2. ___________
3. ___________

Overall Rating: ___/10
```

## 🔧 Debug Tools

### Browser Console
```javascript
// Check comment structure
console.log(displayedComments);

// Check Socket.IO connection
socket.connected

// Check like states
console.log(likingComments);

// Check expanded states
console.log(expandedComments);
```

### React DevTools
- Check component re-renders
- Monitor state changes
- Profile performance

### Network Tab
- Monitor API calls
- Check Socket.IO events
- Verify real-time updates
