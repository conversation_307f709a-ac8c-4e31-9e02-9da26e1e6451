// File: backend/create-admin-user.js
// Script để tạo admin user

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');
require('dotenv').config();

async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Đã kết nối MongoDB');
    } catch (error) {
        console.error('❌ Lỗi kết nối MongoDB:', error);
        process.exit(1);
    }
}

async function createAdminUser() {
    try {
        console.log('🔧 Tạo admin user...');
        
        const adminEmail = '<EMAIL>';
        const adminPassword = 'admin123';
        
        // Kiểm tra xem admin đã tồn tại chưa
        const existingAdmin = await User.findOne({ email: adminEmail });
        if (existingAdmin) {
            console.log('✅ Admin user đã tồn tại:', adminEmail);
            console.log('📧 Email:', existingAdmin.email);
            console.log('👤 Role:', existingAdmin.role);
            return existingAdmin;
        }
        
        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(adminPassword, salt);
        
        // Tạo admin user
        const adminUser = new User({
            fullName: 'Administrator',
            email: adminEmail,
            password: hashedPassword,
            role: 'admin',
            isVerified: true,
            avatarUrl: 'https://via.placeholder.com/150/0000FF/FFFFFF?text=ADMIN'
        });
        
        await adminUser.save();
        
        console.log('✅ Đã tạo admin user thành công!');
        console.log('📧 Email:', adminEmail);
        console.log('🔑 Password:', adminPassword);
        console.log('👤 Role:', adminUser.role);
        
        return adminUser;
        
    } catch (error) {
        console.error('❌ Lỗi khi tạo admin user:', error);
        throw error;
    }
}

async function main() {
    await connectDB();
    await createAdminUser();
    
    console.log('\n🚀 Bây giờ bạn có thể:');
    console.log('1. Đăng nhập vào admin panel với email: <EMAIL>');
    console.log('2. Chạy test APIs: node test-chatbot.js');
    console.log('3. Truy cập giao diện admin: http://localhost:3000/admin');
    
    process.exit(0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Lỗi:', error);
        process.exit(1);
    });
}

module.exports = { createAdminUser };
