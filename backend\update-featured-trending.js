// Update existing posts and topics to have featured and trending flags
require('dotenv').config();
const mongoose = require('mongoose');
const Post = require('./models/Post');
const Topic = require('./models/Topic');
const User = require('./models/User');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
};

const updateFeaturedAndTrending = async () => {
    await connectDB();

    console.log('\n🚀 Updating featured posts and trending topics...\n');

    try {
        // Update some posts to be featured (top 5 posts by views)
        const topPosts = await Post.find()
            .sort({ views: -1 })
            .limit(5);

        if (topPosts.length > 0) {
            const postIds = topPosts.map(post => post._id);
            await Post.updateMany(
                { _id: { $in: postIds } },
                { $set: { featured: true } }
            );
            console.log(`✅ Updated ${topPosts.length} posts to featured`);
        }

        // Update some topics to be trending (top 3 topics by post count)
        const topTopics = await Topic.aggregate([
            {
                $lookup: {
                    from: 'posts',
                    localField: '_id',
                    foreignField: 'topicId',
                    as: 'posts'
                }
            },
            {
                $addFields: {
                    postCount: { $size: '$posts' }
                }
            },
            {
                $sort: { postCount: -1 }
            },
            {
                $limit: 3
            }
        ]);

        if (topTopics.length > 0) {
            const topicIds = topTopics.map(topic => topic._id);
            await Topic.updateMany(
                { _id: { $in: topicIds } },
                { $set: { trending: true } }
            );
            console.log(`✅ Updated ${topTopics.length} topics to trending`);
        }

        // Add some images to posts that don't have them
        const postsWithoutImages = await Post.find({
            $or: [
                { images: { $exists: false } },
                { images: { $size: 0 } }
            ]
        }).limit(10);

        for (let i = 0; i < postsWithoutImages.length; i++) {
            const post = postsWithoutImages[i];
            post.images = [`https://picsum.photos/400/250?random=${i + 10}`];
            await post.save();
        }

        if (postsWithoutImages.length > 0) {
            console.log(`✅ Added images to ${postsWithoutImages.length} posts`);
        }

        // Update topic colors if they don't have them
        const topicsWithoutColors = await Topic.find({
            $or: [
                { color: { $exists: false } },
                { color: '' }
            ]
        });

        const colors = ['#2196F3', '#FF9800', '#9C27B0', '#4CAF50', '#F44336', '#00BCD4', '#FF5722'];

        for (let i = 0; i < topicsWithoutColors.length; i++) {
            const topic = topicsWithoutColors[i];
            topic.color = colors[i % colors.length];
            await topic.save({ validateBeforeSave: false });
        }

        if (topicsWithoutColors.length > 0) {
            console.log(`✅ Added colors to ${topicsWithoutColors.length} topics`);
        }

        console.log('\n📊 Final Statistics:');

        const featuredCount = await Post.countDocuments({ featured: true });
        const trendingCount = await Topic.countDocuments({ trending: true });
        const totalPosts = await Post.countDocuments();
        const totalTopics = await Topic.countDocuments();
        const userCount = await User.countDocuments({ role: 'user' });

        console.log(`👥 Users: ${userCount}`);
        console.log(`📝 Total Posts: ${totalPosts}`);
        console.log(`⭐ Featured Posts: ${featuredCount}`);
        console.log(`📚 Total Topics: ${totalTopics}`);
        console.log(`🔥 Trending Topics: ${trendingCount}`);

        console.log('\n🎉 Update complete!');

    } catch (error) {
        console.error('❌ Error updating data:', error);
    }

    mongoose.connection.close();
};

updateFeaturedAndTrending();
